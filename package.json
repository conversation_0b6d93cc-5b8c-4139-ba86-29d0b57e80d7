{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "node --max-old-space-size=4096 ./node_modules/vite/bin/vite.js build"}, "devDependencies": {"@tailwindcss/forms": "^0.5", "@tailwindcss/typography": "^0.5", "autoprefixer": "^10.4", "laravel-vite-plugin": "^1.2.0", "resolve-url-loader": "^5.0.0", "sass": "^1.3", "sass-loader": "^8.0", "tailwindcss": "^3.4", "tailwindcss-motion": "^1.1.0", "vite": "^6.2.0"}, "dependencies": {"animate.css": "^4.1.1", "apexcharts": "^4.5.0", "heatmap.js": "^2.0.5", "leaflet-heatmap": "^1.0.0", "sweetalert2": "^11.6.13"}}