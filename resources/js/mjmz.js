import Swal from 'sweetalert2';

// Define heatmapLayer globally
let heatmapLayer = null;

// Initialize the heatmap layer
function initializeHeatmap() {
    const cfg = {
        radius: 0.1,
        maxOpacity: 0.9,
        scaleRadius: true,
        useLocalExtrema: false,
        latField: 'lat',
        lngField: 'lng',
        valueField: 'count',
    };

    heatmapLayer = new HeatmapOverlay(cfg);

    const baseLayer = L.tileLayer(
        'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        {
            attribution: 'Map data &copy; <a href="http://openstreetmap.org">OpenStreetMap</a> contributors, <a href="http://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>, Imagery © <a href="http://cloudmade.com">CloudMade</a>',
            maxZoom: 18,
        }
    );

    const map = new L.Map('map', {
        center: new <PERSON><PERSON>LatLng(3.0720198154449463, 101.51626586914062),
        zoom: 9,
        layers: [baseLayer, heatmapLayer],
    });

    // Initialize with empty data
    heatmapLayer.setData({
        max: 0,
        data: [],
    });

    console.log('Heatmap layer initialized.');
}

// Initialize the heatmap layer immediately
initializeHeatmap();

// Event listener for heatmap data updates
window.addEventListener('heatmap-data-updated', (event) => {
    if (!heatmapLayer) {
        console.error('Heatmap layer is not initialized.');
        return;
    }

    const heatMaps = event.detail.heatMaps;

    // Log the received heatmap data
    // console.log('Heatmap data received:', heatMaps);

    // Check if heatMaps is empty
    if (!heatMaps || heatMaps.length === 0) {
        console.log('No heatmap data received. Clearing heatmap layer.');
        // Clear the heatmap layer
        heatmapLayer.setData({
            max: 0,
            data: [],
        });
        return; // Skip further processing
    }

    // Prepare data for the heatmap
    const testData = {
        max: Math.max(...heatMaps.map(point => point.count)), // Calculate max count dynamically
        data: heatMaps,
    };

    // Update the heatmap layer
    heatmapLayer.setData(testData);
});

document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.dom-logout').forEach(button => {
        button.onclick = function () {
            let url = "/logout";

            Swal.fire({
                text: "Adakah anda pasti?",
                allowOutsideClick: false,
                showCancelButton: true,
                confirmButtonColor: "#4D3F7B",
                cancelButtonColor: "#adadad",
                confirmButtonText: "OK",
                width: 300,
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = url;
                }
            });
        };
    });

    document.querySelectorAll('.dom-help').forEach(button => {
        button.onclick = function () {
            Swal.fire({
                icon: "info",
                title: "Bantuan",
                text: "Segala jenis persoalan dan pertanyaan tentang sistem ini boleh dilakukan <NAME_EMAIL>",
                footer: '<a href="mailto:<EMAIL>?subject=Bantuan%20Sistem%20MBI">Hantarkan emel sekarang</a>',
                showConfirmButton: false,
            });
        };
    });
});