/**
 * Global Loader Component for BizAppos MBI
 *
 * This module provides a global loading overlay that can be controlled
 * from anywhere in the application. It uses Alpine.js store for state management.
 *
 * Note: Alpine.js is already available globally via Livewire
 */

// Wait for Alpine to be available, then initialize the store
document.addEventListener('alpine:init', () => {
    // Global loader store
    Alpine.store('globalLoader', {
    isVisible: false,
    message: 'Memproses...',
    
    // Show the loader with optional custom message
    show(message = 'Memproses...') {
        this.message = message;
        this.isVisible = true;
        // Prevent body scrolling when loader is active
        document.body.style.overflow = 'hidden';
        // Dispatch custom event for other components to listen
        window.dispatchEvent(new CustomEvent('global-loader:shown', { 
            detail: { message: this.message } 
        }));
    },
    
    // Hide the loader
    hide() {
        this.isVisible = false;
        // Restore body scrolling
        document.body.style.overflow = '';
        // Dispatch custom event for other components to listen
        window.dispatchEvent(new CustomEvent('global-loader:hidden'));
    },
    
    // Toggle loader visibility
    toggle(message = 'Memproses...') {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show(message);
        }
    }
});

    // Global helper functions for easy access
    window.GlobalLoader = {
    show: (message) => Alpine.store('globalLoader').show(message),
    hide: () => Alpine.store('globalLoader').hide(),
    toggle: (message) => Alpine.store('globalLoader').toggle(message),
    isVisible: () => Alpine.store('globalLoader').isVisible
    };
});

// Auto-hide loader on page navigation (for SPA-like behavior)
document.addEventListener('alpine:navigated', () => {
    Alpine.store('globalLoader').hide();
});

// Auto-hide loader on Livewire navigation
document.addEventListener('livewire:navigated', () => {
    Alpine.store('globalLoader').hide();
});

// Optional: Auto-show loader for Livewire requests (can be disabled if not needed)
document.addEventListener('livewire:start', () => {
    // Uncomment the line below if you want automatic loading for all Livewire requests
    // Alpine.store('globalLoader').show('Memuat...');
});

document.addEventListener('livewire:finish', () => {
    // Auto-hide when Livewire requests complete
    Alpine.store('globalLoader').hide();
});

// Integration with form submissions
document.addEventListener('submit', (e) => {
    // Check if form has data-loader attribute
    const form = e.target;
    if (form.hasAttribute('data-loader')) {
        const message = form.getAttribute('data-loader') || 'Menghantar data...';
        GlobalLoader.show(message);
    }
});

// Integration with AJAX requests (if using fetch or axios)
const originalFetch = window.fetch;
window.fetch = function(...args) {
    // Check if request should show loader
    const options = args[1] || {};
    if (options.showLoader !== false) {
        GlobalLoader.show(options.loaderMessage || 'Memuat...');
    }

    return originalFetch.apply(this, args)
        .finally(() => {
            if (options.showLoader !== false) {
                GlobalLoader.hide();
            }
        });
};

// Keyboard accessibility - ESC key to hide loader (optional)
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && Alpine.store('globalLoader').isVisible) {
        // Only hide if it's been visible for more than 1 second to prevent accidental dismissal
        setTimeout(() => {
            if (Alpine.store('globalLoader').isVisible) {
                Alpine.store('globalLoader').hide();
            }
        }, 100);
    }
});

console.log('Global Loader initialized. Use GlobalLoader.show(), GlobalLoader.hide(), or GlobalLoader.toggle()');
