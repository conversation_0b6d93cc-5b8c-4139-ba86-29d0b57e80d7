/**
 * This injects Tailwind's base styles, which is a combination of
 * Normalize.css and some additional base styles.
 *
 * You can see the styles here:
 * https://unpkg.com/tailwindcss/dist/base.css
 */
@tailwind base;

/**
 * Remove the default box-shadow for invalid elements to prevent
 * inputs in Livewire components showing with a
 * red border by default in Firefox.
 *
 * See: https://github.com/laravel-frontend-presets/tall/issues/7
 */
 input:invalid, textarea:invalid, select:invalid {
    box-shadow: none;
}

/**
 * This injects any component classes registered by plugins.
 */
@tailwind components;

/**
 * Here you would add any of your custom component classes; stuff that you'd
 * want loaded *before* the utilities so that the utilities could still
 * override them.
 *
 * Example:
 *
 * .btn { ... }
 * .form-input { ... }
 */

/**
 * This injects all of Tailwind's utility classes, generated based on your
 * config file.
 */
@tailwind utilities;

/**
 * Here you would add any custom utilities you need that don't come out of the
 * box with Tailwind.
 */
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

@layer base {
  body {
    @apply antialiased;
  }
}

html, body {
  font-family: "Nunito", sans-serif;
  font-style: normal;
}

[x-cloak] {
    display: none;
}

.shadow-full {
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1); 
}

@media (min-width: 768px){
  .main.active{
    margin-left: 0px;
    width: 100%;
  }
}

.z-40{
  z-index: 40;
}

.z-50{
  z-index: 50;
}

.opac-05 {
  opacity: 0.5;
}