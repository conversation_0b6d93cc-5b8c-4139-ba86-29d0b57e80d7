@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">PWA Test Page</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- PWA Status -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold text-blue-900 mb-3">PWA Status</h2>
                <div id="pwa-status" class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="w-3 h-3 rounded-full" id="service-worker-status"></span>
                        <span>Service Worker: <span id="sw-text">Checking...</span></span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-3 h-3 rounded-full" id="manifest-status"></span>
                        <span>Manifest: <span id="manifest-text">Checking...</span></span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-3 h-3 rounded-full" id="install-status"></span>
                        <span>Installable: <span id="install-text">Checking...</span></span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-3 h-3 rounded-full" id="standalone-status"></span>
                        <span>Running as PWA: <span id="standalone-text">Checking...</span></span>
                    </div>
                </div>
            </div>

            <!-- Device Info -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold text-green-900 mb-3">Device Info</h2>
                <div class="space-y-2 text-sm">
                    <div>User Agent: <span id="user-agent" class="font-mono text-xs"></span></div>
                    <div>Platform: <span id="platform"></span></div>
                    <div>Mobile: <span id="is-mobile"></span></div>
                    <div>Online: <span id="is-online"></span></div>
                    <div>Screen: <span id="screen-size"></span></div>
                </div>
            </div>

            <!-- PWA Actions -->
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold text-purple-900 mb-3">PWA Actions</h2>
                <div class="space-y-3">
                    <button id="trigger-install" class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors">
                        Trigger Install Prompt
                    </button>
                    <button id="clear-storage" class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors">
                        Clear PWA Storage
                    </button>
                    <button id="test-offline" class="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors">
                        Test Offline Mode
                    </button>
                </div>
            </div>

            <!-- Local Storage Info -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold text-yellow-900 mb-3">PWA Storage</h2>
                <div class="space-y-2 text-sm">
                    <div>Prompt Shown: <span id="prompt-shown"></span></div>
                    <div>Prompt Dismissed: <span id="prompt-dismissed"></span></div>
                    <div>App Installed: <span id="app-installed"></span></div>
                </div>
            </div>
        </div>

        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-2">Instructions for Testing:</h3>
            <ol class="list-decimal list-inside space-y-1 text-sm text-gray-700">
                <li>Open this page on a mobile device</li>
                <li>Check if the install prompt appears automatically</li>
                <li>Try installing the app using the "Trigger Install Prompt" button</li>
                <li>Test offline functionality by turning off internet connection</li>
                <li>Verify the app works when launched from home screen</li>
            </ol>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check Service Worker
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistration().then(registration => {
            if (registration) {
                document.getElementById('service-worker-status').className = 'w-3 h-3 rounded-full bg-green-500';
                document.getElementById('sw-text').textContent = 'Registered';
            } else {
                document.getElementById('service-worker-status').className = 'w-3 h-3 rounded-full bg-red-500';
                document.getElementById('sw-text').textContent = 'Not Registered';
            }
        });
    } else {
        document.getElementById('service-worker-status').className = 'w-3 h-3 rounded-full bg-red-500';
        document.getElementById('sw-text').textContent = 'Not Supported';
    }

    // Check Manifest
    const manifestLink = document.querySelector('link[rel="manifest"]');
    if (manifestLink) {
        document.getElementById('manifest-status').className = 'w-3 h-3 rounded-full bg-green-500';
        document.getElementById('manifest-text').textContent = 'Available';
    } else {
        document.getElementById('manifest-status').className = 'w-3 h-3 rounded-full bg-red-500';
        document.getElementById('manifest-text').textContent = 'Not Found';
    }

    // Check if installable
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
        deferredPrompt = e;
        document.getElementById('install-status').className = 'w-3 h-3 rounded-full bg-green-500';
        document.getElementById('install-text').textContent = 'Yes';
    });

    // Check if running as PWA
    if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true) {
        document.getElementById('standalone-status').className = 'w-3 h-3 rounded-full bg-green-500';
        document.getElementById('standalone-text').textContent = 'Yes';
    } else {
        document.getElementById('standalone-status').className = 'w-3 h-3 rounded-full bg-red-500';
        document.getElementById('standalone-text').textContent = 'No';
    }

    // Device Info
    document.getElementById('user-agent').textContent = navigator.userAgent;
    document.getElementById('platform').textContent = navigator.platform;
    document.getElementById('is-mobile').textContent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ? 'Yes' : 'No';
    document.getElementById('is-online').textContent = navigator.onLine ? 'Yes' : 'No';
    document.getElementById('screen-size').textContent = `${screen.width}x${screen.height}`;

    // PWA Storage
    document.getElementById('prompt-shown').textContent = localStorage.getItem('pwa-prompt-shown') || 'false';
    document.getElementById('prompt-dismissed').textContent = localStorage.getItem('pwa-prompt-dismissed') || 'false';
    document.getElementById('app-installed').textContent = localStorage.getItem('pwa-installed') || 'false';

    // Actions
    document.getElementById('trigger-install').addEventListener('click', function() {
        if (deferredPrompt) {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                console.log('User choice:', choiceResult.outcome);
                deferredPrompt = null;
            });
        } else {
            alert('Install prompt not available. Try on a mobile device or check if already installed.');
        }
    });

    document.getElementById('clear-storage').addEventListener('click', function() {
        localStorage.removeItem('pwa-prompt-shown');
        localStorage.removeItem('pwa-prompt-dismissed');
        localStorage.removeItem('pwa-installed');
        location.reload();
    });

    document.getElementById('test-offline').addEventListener('click', function() {
        window.location.href = '/offline.html';
    });

    // Update install status after a delay
    setTimeout(() => {
        if (!deferredPrompt) {
            document.getElementById('install-status').className = 'w-3 h-3 rounded-full bg-red-500';
            document.getElementById('install-text').textContent = 'No';
        }
    }, 2000);
});
</script>
@endsection