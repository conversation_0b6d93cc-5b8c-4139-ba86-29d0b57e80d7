@extends('layouts.app')

@section('head-script')
<!-- Date Range Picker CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<style>
.icon-wrapper {
  position: relative;
}
.icon-wrapper .icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}
.input-with-icon {
  padding-left: 35px;
}
</style>
@endsection

@section('content')

    <div class="grid grid-cols-2 mb-5">

        <div>
            <h2 class="text-2xl font-semibold mb-[-6px] pb-0">Statistik</h2>
            <span class="text-gray-500 text-xs mt-0 truncate">Lihat senarai peniaga aktif pada tarikh yang ditetapkan</span>
        </div>
    
    </div>
    <br>
    <livewire:statistics />

@endsection

@push('scripts')
    <!-- Date Range Picker JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

    <script>
        $(function() {
            $('input[name="daterange"]').daterangepicker({
                opens: 'left',
                locale: {
                    format: 'DD/MM/YYYY'
                },
            }, function(start, end, label) {
                Livewire.find('statistics').call('filterData', 'date_range', start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD'));
            });
        });
    </script>

    <script>
        // Define updateDateRange globally so Alpine can access it
        window.updateDateRange = function(start, end) {
            const dateRangeInput = document.getElementById('dateRange');
            if (dateRangeInput) {
                // For example, format the dates as DD/MM/YYYY and update the input value
                dateRangeInput.value = moment(start).format('DD/MM/YYYY') + ' - ' + moment(end).format('DD/MM/YYYY');
            }
        };
    </script>
@endpush