<button 
    {{ $attributes->merge(['class' => 'relative flex items-center justify-center p-2 bg-blue-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed']) }}
    x-data="{ loading: false }"
    @click="loading = true; $el.disabled = true; $el.closest('form').submit()"
    style="min-width: 120px;"> <!-- Set min-width to ensure the button size stays consistent -->

    <!-- Wrapper to maintain the height/size of the button -->
    <div class="flex items-center justify-center space-x-2">
        <!-- Spinner Loading -->
        <svg 
            x-show="loading" 
            class="animate-spin h-5 w-5 text-white"
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4l-2 2-2-2V4a8 8 0 00-6 8z"></path>
        </svg>
    
        <!-- Button Label -->
        <span x-show="!loading">{{ $slot }}</span>
    </div>
</button>