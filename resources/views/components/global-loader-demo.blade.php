{{-- 
    Global Loader Demo Component
    
    This component demonstrates how to use the global loader from different contexts.
    You can include this in any page to test the loader functionality.
--}}

<div class="bg-white p-6 rounded-lg shadow-sm">
    <h3 class="text-lg font-semibold mb-4 text-gray-800">Global Loader Demo</h3>
    <p class="text-gray-600 mb-6">Test the global loading overlay with different scenarios:</p>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {{-- Basic Show/Hide --}}
        <div class="space-y-2">
            <h4 class="font-medium text-gray-700">Basic Controls</h4>
            <button 
                onclick="GlobalLoader.show()"
                class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition duration-200">
                Show Loader
            </button>
            <button 
                onclick="GlobalLoader.hide()"
                class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition duration-200">
                Hide Loader
            </button>
        </div>
        
        {{-- Custom Messages --}}
        <div class="space-y-2">
            <h4 class="font-medium text-gray-700">Custom Messages</h4>
            <button 
                onclick="GlobalLoader.show('Menyimpan data...')"
                class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition duration-200">
                Saving Data
            </button>
            <button 
                onclick="GlobalLoader.show('Memuat laporan...')"
                class="w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded transition duration-200">
                Loading Report
            </button>
        </div>
        
        {{-- Timed Examples --}}
        <div class="space-y-2">
            <h4 class="font-medium text-gray-700">Timed Examples</h4>
            <button 
                onclick="showTimedLoader()"
                class="w-full bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded transition duration-200">
                3 Second Demo
            </button>
            <button 
                onclick="simulateApiCall()"
                class="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition duration-200">
                Simulate API Call
            </button>
        </div>
    </div>
    
    {{-- Alpine.js Example --}}
    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 class="font-medium text-gray-700 mb-3">Alpine.js Integration Example</h4>
        <div x-data="{ processing: false }">
            <button 
                @click="
                    processing = true;
                    $store.globalLoader.show('Processing with Alpine.js...');
                    setTimeout(() => {
                        processing = false;
                        $store.globalLoader.hide();
                    }, 2000);
                "
                :disabled="processing"
                class="bg-indigo-500 hover:bg-indigo-600 disabled:bg-indigo-300 text-white px-4 py-2 rounded transition duration-200">
                <span x-show="!processing">Process with Alpine.js</span>
                <span x-show="processing">Processing...</span>
            </button>
        </div>
    </div>
    
    {{-- Usage Instructions --}}
    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 class="font-medium text-blue-800 mb-2">Usage Instructions</h4>
        <div class="text-sm text-blue-700 space-y-1">
            <p><strong>JavaScript:</strong> <code>GlobalLoader.show('Custom message')</code></p>
            <p><strong>JavaScript:</strong> <code>GlobalLoader.hide()</code></p>
            <p><strong>Alpine.js:</strong> <code>$store.globalLoader.show('Message')</code></p>
            <p><strong>Alpine.js:</strong> <code>$store.globalLoader.hide()</code></p>
            <p><strong>Events:</strong> Listen for 'global-loader:shown' and 'global-loader:hidden'</p>
        </div>
    </div>
</div>

<script>
    // Demo functions
    function showTimedLoader() {
        GlobalLoader.show('Demo akan selesai dalam 3 saat...');
        setTimeout(() => {
            GlobalLoader.hide();
        }, 3000);
    }
    
    function simulateApiCall() {
        GlobalLoader.show('Memanggil API...');
        
        // Simulate API call with fetch
        setTimeout(() => {
            GlobalLoader.show('Memproses respons...');
            setTimeout(() => {
                GlobalLoader.hide();
                alert('API call completed!');
            }, 1000);
        }, 2000);
    }
    
    // Example of listening to loader events
    window.addEventListener('global-loader:shown', (e) => {
        console.log('Global loader shown with message:', e.detail.message);
    });
    
    window.addEventListener('global-loader:hidden', () => {
        console.log('Global loader hidden');
    });
</script>
