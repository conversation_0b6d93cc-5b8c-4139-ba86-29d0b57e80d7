{{-- 
    Global Loader Component for BizAppos MBI
    
    This component provides a full-screen loading overlay that blocks all user interactions.
    It's controlled via Alpine.js store and can be shown/hidden from anywhere in the application.
--}}

<div 
    x-data
    x-show="$store.globalLoader.isVisible"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
    style="display: none;"
    @click.self="$store.globalLoader.hide()"
    role="dialog"
    aria-modal="true"
    aria-labelledby="loader-message"
    aria-describedby="loader-description">
    
    {{-- Loader Content --}}
    <div class="bg-white rounded-lg shadow-xl p-8 max-w-sm mx-4 text-center">
        {{-- Spinner Animation --}}
        <div class="flex justify-center mb-4">
            <svg 
                class="animate-spin h-12 w-12 text-pos"
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24"
                aria-hidden="true">
                <circle 
                    class="opacity-25" 
                    cx="12" 
                    cy="12" 
                    r="10" 
                    stroke="currentColor" 
                    stroke-width="4">
                </circle>
                <path 
                    class="opacity-75" 
                    fill="currentColor" 
                    d="M4 12a8 8 0 018-8v4l-2 2-2-2V4a8 8 0 00-6 8z">
                </path>
            </svg>
        </div>
        
        {{-- Loading Message --}}
        <div 
            id="loader-message"
            class="text-gray-700 font-medium text-lg mb-2"
            x-text="$store.globalLoader.message">
        </div>
        
        {{-- Description --}}
        <div 
            id="loader-description"
            class="text-gray-500 text-sm">
            Sila tunggu sebentar...
        </div>
        
        {{-- Optional: Progress dots animation --}}
        <div class="flex justify-center mt-4 space-x-1">
            <div class="w-2 h-2 bg-pos rounded-full animate-pulse"></div>
            <div class="w-2 h-2 bg-pos rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
            <div class="w-2 h-2 bg-pos rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
        </div>
    </div>
    
    {{-- Invisible overlay to capture all clicks and prevent interaction --}}
    <div 
        class="absolute inset-0 cursor-wait"
        @click.prevent
        @keydown.prevent
        @keyup.prevent
        @keypress.prevent>
    </div>
</div>

{{-- Additional styles for enhanced visual appeal --}}
<style>
    /* Custom pulse animation for dots */
    @keyframes pulse-dot {
        0%, 100% {
            opacity: 0.4;
            transform: scale(1);
        }
        50% {
            opacity: 1;
            transform: scale(1.1);
        }
    }
    
    /* Ensure the loader is above everything */
    .global-loader {
        z-index: 99999 !important;
    }
    
    /* Prevent text selection in loader */
    .global-loader * {
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }
</style>
