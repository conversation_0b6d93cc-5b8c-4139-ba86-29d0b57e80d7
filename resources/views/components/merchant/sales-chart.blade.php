<div id="horizontalBarChart"></div>

@push('scripts')
<script>
            
var options = {
    
            chart: {
                type: 'area',
                height: 350
            },
            series: [{
                name: 'RM',
                data: @json($dailySales['sale'])
            }],
            xaxis: {
                categories: @json($dailySales['date'])
            },
            stroke: {
                curve: 'smooth' // Makes it a spline (smooth) area chart
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.7,
                    stops: [0, 100, 100]
                }
            },
            colors: ['#9C94EA','#4D3F7B'], // Warna untuk setiap series
            dataLabels: {
                enabled: false
            },
            tooltip: {
                x: {
                    format: 'dd/MM/yy HH:mm'
                }
            }
        };

        var chart = new ApexCharts(document.querySelector("#horizontalBarChart"), options);
        chart.render();
    

</script>
@endpush