<h2 class="text-sm font-semibold">TOP PRODUCTS</h2>
<div id="chart"></div>


<script>
    var options = {
        chart: {
            type: 'pie',
        },
        legend: {
            show: true,
            showForSingleSeries: false,
            showForNullSeries: true,
            showForZeroSeries: true,
            position: 'bottom',
            horizontalAlign: 'center', 
            // fontSize: '18px',
            fontFamily: 'Helvetica, Arial',
            fontWeight: 400,
            itemMargin: {
                horizontal: 15,
                vertical: 0
            },
        },
        series: @json($topProducts['series']), // Data untuk setiap jenis produk
        labels: @json($topProducts['labels']), // Nama produk
        colors: ['#FF4560', '#008FFB', '#00E396', '#775DD0', '#FEB019'], // Warna untuk setiap segmen
        
        dataLabels: {
            enabled: true,
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                colors: ['#fff']
            }
        },
        tooltip: {
            enabled: true,
            theme: 'dark',
        }
    };

    var chart = new ApexCharts(document.querySelector("#chart"), options);
    chart.render();
</script>