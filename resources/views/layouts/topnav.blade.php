<div class="shadow-md p-4 w-full bg-white flex justify-between items-center">
    <!-- Other Navigation Items (if any) -->
    <button data-collapse-toggle="navbar-user" type="button"
        class="sidebar-toggle inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600"
        aria-controls="navbar-user" aria-expanded="false">
        <span class="sr-only">Open sidebar</span>
        <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M1 1h15M1 7h15M1 13h15" />
        </svg>
    </button>

    <div class="flex-grow"></div> <!-- For spacing -->

    <!-- Rounded Image -->

    <div class="relative"> 
        <!-- Navbar -->
        <div class="flex flex-row items-center gap-1" onclick="toggleMenu()">
            <div class="me-3 text-xs font-weight-bold">{{ userInfo('username') }}</div>
            <img src="https://images.seeklogo.com/logo-png/36/1/menteri-besar-selangor-incorporated-logo-png_seeklogo-362446.png"
                alt="Profile" loading="lazy" class="rounded-full cursor-pointer" style="width: 50px; height: 50px;"
                id="profileImage">
        </div>

        <!-- Dropdown Menu -->
        <div id="dropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-50">
            <div class="py-2">
                <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-pos">Settings</a>
                <a href="javascript:;" class="dom-logout block px-4 py-2 text-pos hover:bg-gray-100">Logout</a>
            </div>
        </div>
    </div>
</div>
