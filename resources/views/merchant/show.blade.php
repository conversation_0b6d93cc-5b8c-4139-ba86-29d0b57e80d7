@extends('layouts.app')

@section('content')

<div class="grid grid-cols-2 mb-5 motion-translate-y-in-100 motion-duration-[1.2s] motion-ease-spring-smooth">
    <div>
        <h2 class="text-2xl font-semibold mb-[-5px] pb-0">{{ $data->profile->com_name }}</h2>
        {{-- <span class="text-gray-500 text-sm mt-0 truncate">COMPANY INDUSTRY PUT HERE</span> --}}
    </div>

    @php
        $urlPath = request()->path();
        $fromPage = request()->query('from_page', 'merchant'); // 'merchant' is optional and can be replaced with a default value if 'from_page' is not present
        $startDate = request()->query('startdate', ''); 
        $endDate = request()->query('enddate', '');
        $page = request()->query('return_page', 1);
        $filterData = request()->query('filterData', '');
    @endphp

    <div>
        <button 
            onclick="window.location.href='/{{ $fromPage }}?return_page={{ $page }}&startDate={{ $startDate }}&endDate={{ $endDate }}&filterData={{ $filterData }}'" 
            class="text-pos p-2 float-end flex items-center text-sm">
            <x-heroicon-o-arrow-long-left class="mr-1 h-5 w-5" /> Kembali
        </button>
    </div>    
</div>

<div class="grid grid-cols-1 lg:grid-cols-10 mb-5">

    <div class="lg:col-span-3">
        <div class="bg-white p-6 rounded-xl shadow-sm mb-4">
            <x-merchant.business-info 
    :user_name="$data->username"
    :company_name="$data->profile->com_name"
    :company_address="$data->profile->com_address"
    :company_mobile="$data->profile->com_mobile"
    :company_email="$data->profile->com_email"
    :company_status="$data->profile->status"
    :company_pbt="strtoupper($data->pbt?->name ?? 'Tiada Data')"
    :company_register_date="date('d F Y', strtotime($data->profile->created_at))" />


        </div>

        <div class="bg-white p-6 rounded-xl shadow-sm mb-4">           
        @if($top_products && count($top_products) > 0)
            <x-merchant.top-products :topProducts="$top_products" />
        @else
            <div class="text-center py-8">
                
                <p class="text-gray-500 text-sm">Tiada data produk terjual yang mencukupi</p>
                <p class="text-gray-400 text-xs mt-1">Taburan data produk akan dipapar disini.</p>
            </div>
        @endif 
        </div>

        <div class="bg-white p-6 rounded-xl shadow-sm mb-4">
            <x-merchant.payment-channel :paymentChannels="$payment_channels" />
        </div>

    </div>
    <div class="lg:col-span-7 lg:ml-4">

        <x-merchant.total-sales  
            totalSales="{{ moneyFormat($data->total_sales, 2, '') }}" 
            lastActive="{{
                $data->last_active && $data->last_active->updated_at 
                ? \Carbon\Carbon::parse($data->last_active->updated_at)->format('d/m/y h:i:s A') 
                : 'N/A' 
             }}" />
        
        <x-merchant.sales-chart 
        :dailySales="$daily_sales"/>

        {{-- <x-merchant.sales-datalist /> --}}

    </div>
</div>

@endsection
