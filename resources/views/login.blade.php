<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ env('APP_NAME', '') }}</title>
    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ url(asset('images/logo/pos_logo_bak.png')) }}">

    <!-- Fonts -->
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">

    @vite(['resources/sass/app.scss', 'resources/js/app.js'])
    @livewireStyles
</head>
<body class="h-screen bg-blue-600">

    <div class="grid grid-cols-1 lg:grid-cols-2 h-full">

        <!-- Kiri -->
        <div class="flex flex-col items-center justify-center">    
            <div class="mb-6"><image src="{{ asset('images/logo/mbi.png') }}" class="h-20 animate__animated animate__fadeInDown" /></div>        
            <div class="text-white font-thin leading-10 text-5xl animate__animated animate__fadeIn">Analisis Visual</div>
            <div class="text-white font-thin text-5xl animate__animated animate__fadeIn">Prestasi</div>
            <div class="text-white font-thin leading-10 text-5xl animate__animated animate__fadeIn">Usahawan UPLATS</div>
        </div>

        <!-- Login Box -->
        <div class="flex items-center justify-center p-4 bg-gray-50">
            <div class="w-full max-w-sm bg-white border border-gray-200 p-6 rounded-lg shadow-full">
                <h2 class="text-lg font-normal mb-6 text-center">Akses Masuk</h2>

@if ($errors->any())
    <div class="text-red-500 text-xs text-center mb-4">
        @foreach ($errors->all() as $error)
            <div class="p-2 bg-red-50 rounded border border-red-200">
                {{ $error }}
            </div>
        @endforeach
    </div>
@endif

                <form action="{{ route('authenticate')}}" method="POST">
                    @csrf
                    <div class="my-5">
                    <x-forms.input
                        label="ID Pengguna"
                        type="text"
                        name="username"
                        placeholder="ID Pengguna"
                        class="w-full"
                        :value="old('username')"
                    />
                    </div>
                    <div class="my-5">
                    <x-forms.input
                        label="Kata Laluan"
                        type="password"
                        name="password"
                        placeholder="*********"
                        class="w-full"
                    />
                    </div>
                    <div class="flex items-center justify-between">
                        {{-- <button class=" w-full bg-blue-600 hover:bg-blue-700 text-white font-light py-2 px-4 rounded focus:outline-none focus:shadow-outline" type="submit">
                            Sign In
                        </button> --}}
                        <x-forms.loading-button type="submit" class="w-full">Log Masuk</x-forms.loading-button>
                    </div>
                </form>
            </div>
        </div>     
           
    </div>


@livewireScripts
</body>
</html>
