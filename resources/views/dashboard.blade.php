@extends('layouts.app')

@section('head-script')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
@endsection

@section('foot-script')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
@endsection

@section('content')

@php
    $currentTab = $tab ?? 'today';
    $previousYear = date('Y', strtotime('-1 year'));
    $currentYear = date('Y');
@endphp

<div x-data="{
    tab: {!! json_encode($currentTab) !!},
    years: {
        previous: {!! json_encode($previousYear) !!},
        current: {!! json_encode($currentYear) !!}
    }
}" @tab-changed.window="tab = $event.detail.tab">
    <div class="overflow-x-auto">
        <div class="flex flex-wrap md:flex-nowrap space-x-2 md:space-x-4 mb-4 justify-center bg-gray-100 p-4 rounded-lg shadow-sm">
            <button
                wire:click="$set('tab', years.previous)"
                @click="$dispatch('tab-changed', { tab: years.previous }); $store.globalLoader.show('Memuat data ' + years.previous + '...')"
                :class="tab === years.previous 
                    ? 'bg-white text-blue-600 shadow-lg transform scale-105 border-b-2 border-blue-600 rounded-t-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-white hover:text-blue-600 hover:shadow-md'"
                class="px-4 py-2 text-sm md:text-base font-semibold transition-all duration-300 ease-in-out whitespace-nowrap flex-shrink-0">
                <span x-text="years.previous"></span>
            </button>

            <button
                wire:click="$set('tab', years.current)"
                @click="$dispatch('tab-changed', { tab: years.current }); $store.globalLoader.show('Memuat data ' + years.current + '...')"
                :class="tab === years.current 
                    ? 'bg-white text-blue-600 shadow-lg transform scale-105 border-b-2 border-blue-600 rounded-t-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-white hover:text-blue-600 hover:shadow-md'"
                class="px-4 py-2 text-sm md:text-base font-semibold transition-all duration-300 ease-in-out whitespace-nowrap flex-shrink-0">
                <span x-text="years.current"></span>
            </button>

            <button
                @click="$dispatch('tab-changed', { tab: 'today' }); $store.globalLoader.show('Memuat data semasa...')"
                :class="tab === 'today'
                    ? 'bg-white text-blue-600 shadow-lg transform scale-105 border-b-2 border-blue-600 rounded-t-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-white hover:text-blue-600 hover:shadow-md'"
                class="px-4 py-2 text-sm md:text-base font-semibold transition-all duration-300 ease-in-out whitespace-nowrap flex-shrink-0">
                DATA SEMASA
            </button>

            <button
                @click="$dispatch('tab-changed', { tab: 'all' }); $store.globalLoader.show('Memuat semua data...')"
                :class="tab === 'all'
                    ? 'bg-white text-blue-600 shadow-lg transform scale-105 border-b-2 border-blue-600 rounded-t-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-white hover:text-blue-600 hover:shadow-md'"
                class="px-4 py-2 text-sm md:text-base font-semibold transition-all duration-300 ease-in-out whitespace-nowrap flex-shrink-0">
                KESELURUHAN
            </button>
        </div>
    </div>

    {{-- <livewire:report-sales :key="'report-sales'" />
    <livewire:payment-method :key="'payment-method'" />
    <livewire:statistics-active :key="'statistic-active'"/>
    <livewire:company-sales /> --}}
</div>

@endsection

@push('scripts')
<script>
    // Set authentication flag for PWA installer
    if (window.pwaInstaller) {
        window.pwaInstaller.setAuthenticatedUser(true);
    }

    // Alternative: Set a global flag that PWA installer can check
    window.isUserAuthenticated = true;

    // Payment Method Chart initialization
    let paymentChart = null;

    function initPaymentChart(series, labels, colors) {
        console.log('Initializing payment chart with:', { series, labels, colors });

        const chartContainer = document.querySelector("#horizontalBarChart");
        if (!chartContainer) {
            console.error('Payment chart container not found!');
            return;
        }

        if (paymentChart) {
            paymentChart.destroy();
        }

        const options = {
            series: series,
            labels: labels,
            chart: {
                height: 260,
                type: 'pie',
            },
            colors: colors,
            legend: {
                show: true,
                position: 'bottom',
                horizontalAlign: 'center',
            }
        };

        paymentChart = new ApexCharts(chartContainer, options);
        paymentChart.render();
    }

    // Listen for the Livewire event
    document.addEventListener('updateChart', event => {
        const { series, labels, colors } = event.detail[0];
        initPaymentChart(series, labels, colors);
    });

    // Initialize chart on page load
    document.addEventListener('DOMContentLoaded', function() {
        // This will be populated by the Livewire component
        setTimeout(() => {
            const chartContainer = document.querySelector("#horizontalBarChart");
            if (chartContainer && !paymentChart) {
                // Try to get data from Livewire component
                const paymentMethodComponent = Livewire.find('payment-method');
                if (paymentMethodComponent) {
                    // Chart will be initialized by the component
                }
            }
        }, 1000);
    });
</script>
@endpush