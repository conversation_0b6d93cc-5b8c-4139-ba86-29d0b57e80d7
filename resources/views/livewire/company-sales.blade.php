<div
    class="overflow-auto motion-translate-y-in-100 motion-duration-[1.2s] motion-ease-spring-smooth"
    @show-global-loader.window="$store.globalLoader.show($event.detail.message)"
    @hide-global-loader.window="$store.globalLoader.hide()">
    <!-- Filtering Options -->
    <div class="bg-white p-4 rounded-xl shadow-sm mb-4">
        <div class="flex flex-wrap gap-4 items-center">
            <!-- Search Bar -->
            <div class="flex-grow">
                <label class="block text-sm font-medium text-gray-700 mb-1">Carian</label>
                <div class="relative">
                    <input type="text" wire:model="search" wire:keydown.enter="performSearch"
                        placeholder="Carian nama syarikat atau nama peniaga..."
                        class="w-full border border-gray-300 rounded-lg pl-4 pr-10 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150"
                        style="padding-right: 2.5rem;" />

                </div>

            </div>

            <!-- Sales Range Inputs -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Jualan Minima (RM)</label>
                <input type="number" wire:model="filters.salesRange.min" placeholder="0.00"
                    class="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Jualan Maksima (RM)</label>
                <input type="number" wire:model="filters.salesRange.max" placeholder="0.00"
                    class="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">PBT</label>
                <select wire:model="filters.com_pbt"
                    class="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                    <option value="0">SEMUA</option>
                    @foreach ($pbts as $pbt)
                        <option value="{{ $pbt->id }}">{{ $pbt->code }}</option>
                    @endforeach
                </select>
            </div>
        </div>

        <br>
        <!-- Filter Actions -->
        <div class="mt-4 flex gap-2">
            <button wire:click="applyFilters"
                class="btn btn-primary px-4 py-2 text-sm font-medium bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 transition duration-150"
                wire:loading.attr="disabled" wire:target="applyFilters">
                <span wire:loading.remove wire:target="applyFilters">Tapis</span>
                <span wire:loading wire:target="applyFilters">Loading...</span>
            </button>

            <button wire:click="resetFilters"
                class="btn btn-secondary px-4 py-2 text-sm font-medium bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 focus:ring-2 focus:ring-gray-500 transition duration-150"
                wire:loading.attr="disabled" wire:target="resetFilters">
                Buang Tapisan
            </button>
        </div>
    </div>
    <!-- Loading State -->
    <div wire:loading wire:target="fetchRecord" class="my-4">
        <div class="alert alert-info">
            Loading...
        </div>
    </div>


    <!-- Error Message -->
    @if ($errorMessage)
        <div class="alert alert-danger my-4">
            {{ $errorMessage }}
        </div>
    @endif

    @php
        $urlPath = request()->path();
        $segments = explode('/', $urlPath);
        $fromPath = $segments[0];
        $fromPage = request()->query('from_page', 'merchant'); // 'page' is optional and can be replaced with a default value if 'from_page' is not present
    @endphp

    <table class="w-full sm:bg-white rounded-lg overflow-hidden border border-gray-700 sm:shadow-lg mt-3">
        <thead class="text-white uppercase text-xs">
            <tr class="bg-pos-light-200 rounded-l-lg sm:rounded-none mb-2 sm:mb-0 w-full">
                <th class="p-3 text-center">#</th>
                <th class="p-3 text-left border border-gray-200 cursor-pointer" wire:click="sortTable('first_name')">
                    <div class="flex justify-between items-center">
                        Nama Peniaga
                        @if ($sortDir === 'ASC')
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 15l7-7 7 7" />
                            </svg>
                        @else
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7" />
                            </svg>
                        @endif
                    </div>
                </th>
                <th class="p-3 text-left border border-gray-200 cursor-pointer" wire:click="sortTable('com_name')">
                    <div class="flex justify-between items-center">
                        Nama Perniagaan
                        @if ($sortDir === 'ASC')
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 15l7-7 7 7" />
                            </svg>
                        @else
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7" />
                            </svg>
                        @endif
                    </div>
                </th>
                <th class="p-3 text-left border border-gray-200 cursor-pointer" wire:click="sortTable('pbt_code')">
                    <div class="flex justify-between items-center">
                        PBT
                        @if ($sortDir === 'ASC')
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 15l7-7 7 7" />
                            </svg>
                        @else
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7" />
                            </svg>
                        @endif
                    </div>
                </th>
                <th class="p-3 text-right border border-gray-200 cursor-pointer"
                    wire:click="sortTable('total_sales')">
                    <div class="flex justify-between items-center">
                        Jualan Terkumpul
                        @if ($sortDir === 'ASC')
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 15l7-7 7 7" />
                            </svg>
                        @else
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7" />
                            </svg>
                        @endif
                    </div>
                </th>
            </tr>
        </thead>
        <tbody>
            @if ($companies->count() > 0)
                @foreach ($companies as $company)
                    <tr class="text-xs text-gray-500 hover:bg-gray-100 hover:cursor-pointer">
                        <td class="p-3 text-center border border-gray-200 whitespace-nowrap">
                            {{ ($companies->currentPage() - 1) * $companies->perPage() + $loop->iteration }}
                        </td>
                        <td class="p-3 border border-gray-200 whitespace-nowrap uppercase">
                            <a href="{{ route('merchant.show', $company->user_id) }}"
                               onclick="GlobalLoader.show('Memuat profil peniaga...')">{{ $company->first_name }}</a>
                        </td>
                        <td class="p-3 border border-gray-200 whitespace-nowrap uppercase">{{ $company->com_name }}</td>
                        <td class="p-3 text-left border border-gray-200 whitespace-nowrap">
                            {{ $company->pbt_code ?? 'ALAMAT TIDAK LENGKAP' }}
                        </td>
                        <td class="p-3 text-right border border-gray-200 whitespace-nowrap">
                            {{ 'RM ' . number_format($company->total_sales, 2) }}
                        </td>
                    </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="5" class="text-center p-4 items-center text-gray-500 text-xs uppercase">
                        Tiada rekod dijumpai
                    </td>
                </tr>
            @endif
        </tbody>

    </table>

    <div class="mt-4">
        {{ $companies->links() }}
    </div>
</div>

