<div
    class="grid grid-cols-1 lg:grid-cols-4 md:grid-cols-2 gap-4 motion-translate-y-in-100 motion-duration-[1.2s] motion-ease-spring-smooth"
    @show-global-loader.window="$store.globalLoader.show($event.detail.message)"
    @hide-global-loader.window="$store.globalLoader.hide()">
    <x-card class="rounded-xl">
        <div class="px-6 py-4">
            @if (!$errorMessage)
                <div class="rounded-full bg-pos-light h-10 w-10 mb-5 flex items-center justify-center">
                    <x-heroicon-s-chart-pie class="text-pos h-8 w-8 p-1" />
                </div>
                <div class="text-gray-500 text-sm font-semibold">{{ $sales_year_title }}</div>
                <div class="font-bold text-3xl mb-2 truncate">{{ $sales_year_value }}</div>
                <div class="my-5 text-md flex items-center justify-between">
                    <span class="text-pos-light-200">{{ $sales_last_year_title }} : {{ $sales_last_year_value }}</span>
                    <span
                        class="inline-block rounded px-2 py-1 text-sm text-gray-700 {{ $growth_sales_yearly_bg == 'bg-error' ? 'bg-red-200' : 'bg-green-100' }}">
                        {{ $growth_sales_yearly_value }}
                    </span>
                </div>
            @else
                <div class="flex text-center items-center justify-center">
                    <p
                        class="flex text-sm items-center animate__animated animate__fadeIn animate__slow animate__infinite infinite">
                        {{ $errorMessage }}
                    </p>
                </div>
            @endif
        </div>
    </x-card>

    <x-card class="rounded-xl">
        <div class="px-6 py-4">
            @if (!$errorMessage)
                <div class="rounded-full bg-pos-light h-10 w-10 mb-5 flex items-center justify-center">
                    <x-heroicon-o-arrows-right-left class="text-pos h-8 w-8 p-1" />
                </div>
                <div class="text-gray-500 text-sm font-semibold">{{ $total_transaction_year_title }}</div>
                <div class="font-bold text-3xl mb-2 truncate">{{ $total_transaction_year_value }}</div>
                <div class="my-5 text-md">
                    <span
                        class="inline-block py-1 text-pos-light-200 mr-2 mb-2">{{ $total_transaction_last_year_title }}</span>
                    <span
                        class="inline-block rounded float-end px-2 py-1 text-sm text-gray-700 mr-2 mb-2 {{ $growth_transaction_yearly_bg == 'bg-error' ? 'bg-red-200' : 'bg-green-100' }}">{{ $growth_transaction_yearly_value }}</span>
                </div>
            @else
                <div class="flex text-center items-center justify-center">
                    <p
                        class="flex text-sm items-center animate__animated animate__fadeIn animate__slow animate__infinite infinite">
                        {{ $errorMessage }}
                    </p>
                </div>
            @endif
        </div>
    </x-card>

    <x-card class="rounded-xl">
        <a href="{{ route('statistics') }}" onclick="GlobalLoader.show('Memuat halaman statistik...')">
            <div class="px-6 py-4">
                @if (!$errorMessage)
                    <div class="rounded-full bg-pos-light h-10 w-10 mb-5 flex items-center justify-center">
                        <x-heroicon-s-chart-pie class="text-pos h-8 w-8 p-1" />
                    </div>
                    <div class="text-gray-500 text-sm font-semibold">Jumlah Jualan Hari ini</div>
                    <div class="font-bold text-3xl mb-2 truncate">{{ $sales_today }}</div>
                    <div class="text-pos-light-200 my-5 text-md antialiased">Jualan semalam : {{ $sales_yesterday }}
                    </div>
                @else
                    <div class="flex text-center items-center justify-center">
                        <p
                            class="flex text-sm items-center animate__animated animate__fadeIn animate__slow animate__infinite infinite">
                            {{ $errorMessage }}
                        </p>
                    </div>
                @endif
            </div>
        </a>
    </x-card>

    <x-card class="rounded-xl">
        <a href="{{ route('statistics') }}" onclick="GlobalLoader.show('Memuat halaman statistik...')">
            <div class="px-6 py-4">
                @if (!$errorMessage)
                    <div class="rounded-full bg-pos-light h-10 w-10 mb-5 flex items-center justify-center">
                        <x-heroicon-o-arrows-right-left class="text-pos h-8 w-8 p-1" />
                    </div>
                    <div class="text-gray-500 text-sm font-semibold">Transaksi Hari Ini</div>
                    <div class="font-bold text-3xl mb-2 truncate">{{ $total_transaction_today }}</div>
                    <div class="my-5 text-md">
                        <span class="inline-block py-1 text-pos-light-200 mr-2 mb-2">Dari semalam</span>
                        <span
                            class="inline-block rounded float-end px-2 py-1 text-sm text-gray-700 mr-2 mb-2 {{ $growth_transaction_daily_bg == 'bg-error' ? 'bg-red-100' : 'bg-green-100' }}">{{ $growth_transaction_daily_value }}</span>
                    </div>
                @else
                    <div class="flex text-center items-center justify-center">
                        <p
                            class="flex text-sm items-center animate__animated animate__fadeIn animate__slow animate__infinite infinite">
                            {{ $errorMessage }}
                        </p>
                    </div>
                @endif
            </div>
        </a>
    </x-card>
</div>
