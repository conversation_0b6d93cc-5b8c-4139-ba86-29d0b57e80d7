<div
    x-data="{ selected: '{{ $filterType }}' }"
    class="motion-translate-y-in-100 motion-duration-[1.2s] motion-ease-spring-smooth"
    @show-global-loader.window="$store.globalLoader.show($event.detail.message)"
    @hide-global-loader.window="$store.globalLoader.hide()">
    <div>
        <h1 class="text-xl"><PERSON><PERSON><PERSON></h1>
        <div class="mb-5">
            <button class="px-4 py-2 rounded"
                :class="selected === 'today' ? 'bg-blue-500 text-white' : 'bg-white text-blue-500'"
                @click="selected = 'today'; $wire.filterData('today'); updateDateRange('{{ now()->toDateString() }}', '{{ now()->toDateString() }}')">
                Hari Ini
            </button>
            <button class="px-4 py-2 rounded"
                :class="selected === 'yesterday' ? 'bg-blue-500 text-white' : 'bg-white text-blue-500'"
                @click="selected = 'yesterday'; $wire.filterData('yesterday'); updateDateRange('{{ now()->subDay()->toDateString() }}', '{{ now()->subDay()->toDateString() }}')">
                Semalam
            </button>
            <button class="px-4 py-2 rounded"
                :class="selected === 'this_week' ? 'bg-blue-500 text-white' : 'bg-white text-blue-500'"
                @click="selected = 'this_week'; $wire.filterData('this_week'); updateDateRange('{{ now()->startOfWeek()->toDateString() }}', '{{ now()->endOfWeek()->toDateString() }}')">
                Mingguan
            </button>
            <button class="px-4 py-2 rounded"
                :class="selected === 'this_month' ? 'bg-blue-500 text-white' : 'bg-white text-blue-500'"
                @click="selected = 'this_month'; $wire.filterData('this_month'); updateDateRange('{{ now()->startOfMonth()->toDateString() }}', '{{ now()->endOfMonth()->toDateString() }}')">
                Bulanan
            </button>
            <div class="icon-wrapper" style="display: inline-block;">
                <input type="text" name="daterange" id="dateRange" x-ref="dateRange"
                    class="input-with-icon px-4 py-2 border rounded" placeholder="Date Range" />
                <svg class="icon h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <svg class="icon h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
            </div>
        </div>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-2 md:grid-cols-2 gap-4">

        <x-card class="rounded-xl">
            <div class="px-6 py-4 text-right">
                @if (!$errorMessage)
                    <div class="rounded-full bg-pos-light h-10 w-10 mb-5 flex items-center justify-center">
                        <x-heroicon-s-chart-pie class="text-pos h-8 w-8 p-1" />
                    </div>
                    <div class="text-gray-500 text-sm font-semibold">Jumlah Jualan </div>
                    <div class="font-bold text-3xl mb-2 truncate">{{ $sales ?? '0.00' }}</div>
                @else
                    <div class="flex text-center items-center justify-center">
                        <p
                            class="flex text-sm items-center animate__animated animate__fadeIn animate__slow animate__infinite infinite">
                            {{ $errorMessage }}
                        </p>
                    </div>
                @endif
            </div>
        </x-card>

        <x-card class="rounded-xl">
            <div class="px-6 py-4 text-right">
                @if (!$errorMessage)
                    <div class="rounded-full bg-pos-light h-10 w-10 mb-5 flex items-center justify-center">
                        <x-heroicon-o-arrows-right-left class="text-pos h-8 w-8 p-1" />
                    </div>
                    <div class="text-gray-500 text-sm font-semibold">Jumlah Transaksi </div>
                    <div class="font-bold text-3xl mb-2 truncate">{{ $total_transaction ?? '0' }}</div>
                @else
                    <div class="flex text-center items-center justify-center">
                        <p
                            class="flex text-sm items-center animate__animated animate__fadeIn animate__slow animate__infinite infinite">
                            {{ $errorMessage }}
                        </p>
                    </div>
                @endif
            </div>
        </x-card>

        {{-- <x-card class="px-6 py-4 rounded-xl h-full">
            <div id="horizontalBarChart2"></div>
        </x-card> --}}

    </div>

    {{-- USER TABLE STARTS HERE --}}
    <div class="overflow-auto">
        <!-- Filtering Options -->
        {{-- <div class="bg-white p-4 rounded-xl shadow-sm mb-4">
         <div class="flex flex-wrap gap-4 items-center"> --}}
        <!-- Search Bar -->
        {{-- <div class="flex-grow">
                 <label class="block text-sm font-medium text-gray-700 mb-1">Carian</label>
                 <div class="relative">
                     <input 
                         type="text" 
                         wire:model="search"
                         wire:keydown.enter="performSearch"
                         placeholder="Carian nama syarikat atau nama peniaga..." 
                         class="w-full border border-gray-300 rounded-lg pl-4 pr-10 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150"
                         style="padding-right: 2.5rem;"
                     />
                    
                 </div>
                 
             </div> --}}

        <!-- Sales Range Inputs -->
        {{-- <div>
                 <label class="block text-sm font-medium text-gray-700 mb-1">Jualan Minima (RM)</label>
                 <input 
                     type="number" 
                     wire:model="filters.salesRange.min"
                     placeholder="0.00"
                     class="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150"
                 >
             </div>
     
             <div>
                 <label class="block text-sm font-medium text-gray-700 mb-1">Jualan Maksima (RM)</label>
                 <input 
                     type="number" 
                     wire:model="filters.salesRange.max"
                     placeholder="0.00"
                     class="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150"
                 >
             </div>
     
            
         </div>
     
         <br> --}}
        <!-- Filter Actions -->
        {{-- <div class="mt-4 flex gap-2">
             <button 
                 wire:click="applyFilters"
                 class="btn btn-primary px-4 py-2 text-sm font-medium bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 transition duration-150"
                 wire:loading.attr="disabled"
                 wire:target="applyFilters"
             >
                 <span wire:loading.remove wire:target="applyFilters">Tapis</span>
                 <span wire:loading wire:target="applyFilters">Loading...</span>
             </button>
     
             <button 
                 wire:click="resetFilters"
                 class="btn btn-secondary px-4 py-2 text-sm font-medium bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 focus:ring-2 focus:ring-gray-500 transition duration-150"
                 wire:loading.attr="disabled"
                 wire:target="resetFilters"
             >
                 Buang Tapisan
             </button>
             </div>
         </div> --}}
        <!-- Loading State -->
        <div wire:loading wire:target="fetchRecord" class="my-4">
            <div class="alert alert-info">
                Loading...
            </div>
        </div>

        <!-- Error Message -->
        @if ($errorMessage)
            <div class="alert alert-danger my-4">
                {{ $errorMessage }}
            </div>
        @endif

        <table class="w-full sm:bg-white rounded-lg overflow-hidden border border-gray-700 sm:shadow-lg mt-3">
            <thead class="text-white uppercase text-xs">
                <tr class="bg-pos-light-200 rounded-l-lg sm:rounded-none mb-2 sm:mb-0 w-full">
                    <th class="p-3 text-center cursor-pointer" wire:click="">#</th>
                    <th class="p-3 text-left border border-gray-200 cursor-pointer"
                        wire:click="sortTable('first_name')">
                        <div class="flex justify-between items-center">
                            Nama Peniaga
                            @if ($sortDir === 'ASC')
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 15l7-7 7 7" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            @endif
                        </div>
                    </th>
                    <th class="p-3 text-left border border-gray-200 cursor-pointer" wire:click="sortTable('com_name')">
                        <div class="flex justify-between items-center">
                            Nama Perniagaan
                            @if ($sortDir === 'ASC')
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 15l7-7 7 7" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            @endif
                        </div>
                    </th>
                    <th class="p-3 text-left border border-gray-200 cursor-pointer" wire:click="sortTable('code')">
                        <div class="flex justify-between items-center">
                            PBT
                            @if ($sortDir === 'ASC')
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 15l7-7 7 7" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            @endif
                        </div>
                    </th>
                    <th class="p-3 text-left border border-gray-200 cursor-pointer"
                        wire:click="sortTable('total_trx')">
                        <div class="flex justify-between items-center">
                            Transaksi
                            @if ($sortDir === 'ASC')
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 15l7-7 7 7" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            @endif
                        </div>
                    </th>
                    <th class="p-3 text-right border border-gray-200 cursor-pointer"
                        wire:click="sortTable('total_sales')">
                        <div class="flex justify-between items-center">
                            Jualan Terkumpul
                            @if ($sortDir === 'ASC')
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 15l7-7 7 7" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            @endif
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                @if ($user_data->isEmpty())
                    <tr>
                        <td colspan="6" class="text-center p-4 items-center text-gray-500 text-xs uppercase">
                            Tiada rekod dijumpai
                        </td>
                    </tr>
                @else
                    @foreach ($user_data as $company)
                        <tr class="text-xs text-gray-500 hover:bg-gray-100 hover:cursor-pointer">
                            <td class="p-3 text-center border border-gray-200 whitespace-nowrap">
                                {{ ($user_data->currentPage() - 1) * $user_data->perPage() + $loop->iteration }}
                            </td>
                            <td class="p-3 border border-gray-200 whitespace-nowrap uppercase">
                                <a href="{{ route('merchant.show', $company->user_id) }}"
                                   onclick="GlobalLoader.show('Memuat profil peniaga...')">{{ $company->first_name }}</a>
                            </td>
                            <td class="p-3 border border-gray-200 whitespace-nowrap uppercase">
                                {{ $company->com_name }}</td>
                            <td class="p-3 text-left border border-gray-200 whitespace-nowrap">
                                {{ $company->code ?? 'ALAMAT TIDAK LENGKAP' }}
                            </td>
                            <td class="p-3 text-left border border-gray-200 whitespace-nowrap">
                                {{ $company->total_trx }}
                            </td>
                            <td class="p-3 text-right border border-gray-200 whitespace-nowrap">
                                {{ moneyFormat($company->total_sales) }}
                            </td>
                        </tr>
                    @endforeach
                @endif
            </tbody>
        </table>
        <div class="mt-4">
            {{ $user_data->links() }}
        </div>
    </div>

</div>
