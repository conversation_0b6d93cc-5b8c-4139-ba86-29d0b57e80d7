<div
    class="grid grid-cols-1 md:grid-cols-2 gap-4 my-4 motion-translate-y-in-100 motion-duration-[1.2s] motion-ease-spring-smooth"
    @show-global-loader.window="$store.globalLoader.show($event.detail.message)"
    @hide-global-loader.window="$store.globalLoader.hide()">

    <div>
        <div wire:ignore id="map" class="flex col-span-2 relative rounded-xl h-full"></div>
    </div>

    <div class="grid grid-cols-1 gap-6">

        <div class="flex flex-col justify-between h-full space-y-4">
            <x-card class="rounded-xl h-1/2">
                <div class="px-6 py-4">
                    <div class="text-gray-500 text-sm font-semibold">Kawasan PBT Aktif (Selangor)</div>
                    <div class="text-4xl font-bold mb-3 mt-3">{{ $pbtActive }} / {{ $pbtTotal }}</div>
                </div>
            </x-card>
            <x-card class="rounded-xl h-1/2">
                <div class="px-6 py-4">
                    @if ($year == date('Y'))
                        <div class="text-gray-500 text-sm font-semibold">Peniaga Aktif Hari Ini</div>
                     @else
                        <div class="text-gray-500 text-sm font-semibold">Peniaga Aktif </div>   
                    @endif
                    <div class="text-4xl font-bold mb-3 mt-3">{{ $merchantActive }} / {{ $merchantTotal }}</div>
                </div>
            </x-card>
            {{-- <x-card class="rounded-xl h-1/3">
                <div class="px-6 py-4">
                    <div class="text-gray-500 text-sm font-semibold">Kategori Peniaga</div>
                    <div class="text-4xl font-bold mb-3 mt-3">{{ $newCategory }} / {{ $newTotal }}</div>
                </div>
            </x-card> --}}
        </div>

        {{-- <div class="flex flex-col justify-between h-full space-y-4">

            <x-card class="rounded-xl h-full">    
                <div class="flex overflow-hidden flex-col justify-end col-span-1 bg-cover h-full" style="background-image: url({{asset('images/transaction.png')}});">
                    <div class="flex flex-col px-6 py-4" style="background-image: linear-gradient(0deg,#fff,#ffffffd7 50%,transparent)">            
                        <div class="text-gray-950 text-sm gap-2 font-bold">Jumlah Pelanggan</div>
                        <div class="text-4xl font-bold mb-3 mt-3" id="total_customer">{{ $totalCustomer }}</div>
                    </div>
                </div>
            </x-card>
            
        </div> --}}

    </div>

</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/heatmap.js/2.0.0/heatmap.js"></script>
<script src="https://cdn.jsdelivr.net/npm/leaflet-heatmap@1.0.0/leaflet-heatmap.min.js"></script>
@endpush