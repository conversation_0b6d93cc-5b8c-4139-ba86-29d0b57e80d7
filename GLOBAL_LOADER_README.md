# Global Loader Component - BizAppos MBI

A lightweight, performant global loading overlay component that blocks all user interactions during background processing operations.

## Features

- ✅ Full-screen overlay that blocks all user interactions
- ✅ Spinning animation with custom messages
- ✅ Alpine.js store-based state management
- ✅ Easy integration with Livewire components
- ✅ Keyboard accessibility (ESC to dismiss)
- ✅ Automatic integration with form submissions and AJAX requests
- ✅ Custom event dispatching for advanced integrations
- ✅ Consistent styling that matches the application's design system
- ✅ Lightweight and performant implementation

## Installation

The global loader is already integrated into the BizAppos MBI application. The following files have been added/modified:

### New Files
- `resources/js/global-loader.js` - Alpine.js store and helper functions
- `resources/views/components/global-loader.blade.php` - Loader component
- `resources/views/components/global-loader-demo.blade.php` - Demo component

### Modified Files
- `resources/js/app.js` - Added global loader import
- `resources/views/layouts/base.blade.php` - Added loader component to base layout

## Usage

### Basic JavaScript Usage

```javascript
// Show loader with default message
GlobalLoader.show();

// Show loader with custom message
GlobalLoader.show('Menyimpan data...');

// Hide loader
GlobalLoader.hide();

// Toggle loader
GlobalLoader.toggle('Processing...');

// Check if loader is visible
if (GlobalLoader.isVisible()) {
    console.log('Loader is currently visible');
}
```

### Alpine.js Usage

```html
<!-- In your Alpine.js components -->
<div x-data="{ processing: false }">
    <button @click="
        processing = true;
        $store.globalLoader.show('Processing...');
        setTimeout(() => {
            processing = false;
            $store.globalLoader.hide();
        }, 2000);
    ">
        Process Data
    </button>
</div>
```

### Livewire Integration

```php
// In your Livewire component
public function processData()
{
    // The loader can be controlled from the frontend
    $this->dispatch('show-loader', message: 'Processing data...');
    
    // Your processing logic here
    sleep(2);
    
    $this->dispatch('hide-loader');
}
```

```html
<!-- In your Livewire view -->
<div 
    @show-loader.window="$store.globalLoader.show($event.detail.message)"
    @hide-loader.window="$store.globalLoader.hide()">
    
    <button wire:click="processData">
        Process Data
    </button>
</div>
```

### Form Integration

```html
<!-- Automatic loader for form submissions -->
<form data-loader="Submitting form..." action="/submit" method="POST">
    @csrf
    <input type="text" name="data" required>
    <button type="submit">Submit</button>
</form>
```

### AJAX/Fetch Integration

```javascript
// Automatic loader for fetch requests
fetch('/api/data', {
    method: 'POST',
    loaderMessage: 'Fetching data...',
    // showLoader: false // Set to false to disable automatic loader
})
.then(response => response.json())
.then(data => console.log(data));
```

## Events

The global loader dispatches custom events that you can listen to:

```javascript
// Listen for loader shown event
window.addEventListener('global-loader:shown', (e) => {
    console.log('Loader shown with message:', e.detail.message);
});

// Listen for loader hidden event
window.addEventListener('global-loader:hidden', () => {
    console.log('Loader hidden');
});
```

## Customization

### Styling

The loader uses Tailwind CSS classes and can be customized by modifying the component file:
- `resources/views/components/global-loader.blade.php`

Key styling classes:
- `bg-pos` - Uses the application's primary color
- `z-[9999]` - Ensures loader appears above all content
- `backdrop-blur-sm` - Adds subtle blur effect to background

### Messages

Default messages are in Bahasa Malaysia to match the application's language:
- Default: "Memproses..."
- Form submission: "Menghantar data..."
- AJAX requests: "Memuat..."

### Animation

The loader includes:
- Fade in/out transitions (300ms enter, 200ms leave)
- Spinning SVG animation
- Pulsing dots animation
- Smooth backdrop blur effect

## Best Practices

1. **Use descriptive messages**: Provide context about what's being processed
   ```javascript
   GlobalLoader.show('Menyimpan laporan penjualan...');
   ```

2. **Always hide the loader**: Ensure every `show()` call has a corresponding `hide()`
   ```javascript
   try {
       GlobalLoader.show('Processing...');
       await processData();
   } finally {
       GlobalLoader.hide();
   }
   ```

3. **Use with async operations**: Perfect for API calls, file uploads, data processing
   ```javascript
   async function uploadFile(file) {
       GlobalLoader.show('Memuat naik fail...');
       try {
           const result = await uploadAPI(file);
           return result;
       } finally {
           GlobalLoader.hide();
       }
   }
   ```

4. **Avoid for very short operations**: Don't use for operations under 500ms
   ```javascript
   // Good
   setTimeout(() => GlobalLoader.show(), 500); // Delay for short operations
   
   // Bad
   GlobalLoader.show();
   setTimeout(() => GlobalLoader.hide(), 100); // Too short
   ```

## Accessibility

- Uses proper ARIA attributes (`role="dialog"`, `aria-modal="true"`)
- Keyboard accessible (ESC key to dismiss after 1 second)
- Screen reader friendly with descriptive labels
- Prevents text selection and interaction with background content

## Browser Support

- Modern browsers with ES6+ support
- Alpine.js v3.x compatible
- Tailwind CSS v3.x compatible

## Troubleshooting

### Loader not showing
1. Check browser console for JavaScript errors
2. Ensure Alpine.js is loaded before the global loader script
3. Verify the component is included in the base layout

### Loader not hiding
1. Check for JavaScript errors that prevent the hide() call
2. Ensure every show() has a corresponding hide()
3. Use browser dev tools to check the Alpine.js store state

### Styling issues
1. Verify Tailwind CSS is compiled with the loader classes
2. Check for CSS conflicts with z-index values
3. Ensure the `pos` color is defined in Tailwind config

## Demo

Visit the dashboard to see the global loader demo component with various usage examples.

## Support

For issues or questions, contact the development team or refer to the main application documentation.
