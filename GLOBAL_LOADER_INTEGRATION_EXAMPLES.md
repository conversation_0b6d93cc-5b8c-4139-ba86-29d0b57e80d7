# Global Loader Integration Examples

This document provides practical examples of how to integrate the global loader with existing BizAppos MBI components.

## 1. Livewire Component Integration

### Example: Adding loader to StatisticsActive component

**In the Livewire component (app/Livewire/StatisticsActive.php):**

```php
public function handleTabChange($tab)
{
    // Dispatch event to show loader
    $this->dispatch('show-global-loader', message: 'Memuat data statistik...');
    
    $this->tab = $tab;
    $this->fetchStatisticsActiveData($tab);
    
    // Dispatch event to hide loader
    $this->dispatch('hide-global-loader');
}

public function fetchStatisticsActiveData($tab)
{
    try {
        // Your existing API call code...
        
        // Hide loader on success
        $this->dispatch('hide-global-loader');
        
    } catch (Exception $e) {
        // Hide loader on error
        $this->dispatch('hide-global-loader');
        $this->errorMessage = $e->getMessage();
    }
}
```

**In the Livewire view (resources/views/livewire/statistics-active.blade.php):**

```html
<div 
    @show-global-loader.window="$store.globalLoader.show($event.detail.message)"
    @hide-global-loader.window="$store.globalLoader.hide()">
    
    <!-- Your existing component content -->
    
</div>
```

## 2. Form Submission Integration

### Automatic Integration with data-loader attribute:

```html
<form data-loader="Menyimpan data peniaga..." action="/merchant/save" method="POST">
    @csrf
    <input type="text" name="merchant_name" required>
    <button type="submit">Simpan</button>
</form>
```

### Manual Integration with Alpine.js:

```html
<form x-data="{ submitting: false }" 
      @submit="submitting = true; $store.globalLoader.show('Menyimpan data...')">
    @csrf
    <input type="text" name="merchant_name" required>
    <button type="submit" :disabled="submitting">
        <span x-show="!submitting">Simpan</span>
        <span x-show="submitting">Menyimpan...</span>
    </button>
</form>
```

## 3. AJAX/Fetch Integration

### Automatic Integration:

```javascript
// Loader will show automatically
fetch('/api/merchant-data', {
    method: 'POST',
    loaderMessage: 'Memuat data peniaga...',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({ merchant_id: 123 })
})
.then(response => response.json())
.then(data => {
    console.log('Data loaded:', data);
    // Loader will hide automatically
});
```

### Manual Integration:

```javascript
async function loadMerchantData(merchantId) {
    try {
        GlobalLoader.show('Memuat data peniaga...');
        
        const response = await fetch(`/api/merchant/${merchantId}`, {
            showLoader: false // Disable automatic loader since we're handling it manually
        });
        
        const data = await response.json();
        
        // Process data...
        
    } catch (error) {
        console.error('Error loading merchant data:', error);
    } finally {
        GlobalLoader.hide();
    }
}
```

## 4. Button Click Integration

### Simple Button with Alpine.js:

```html
<button 
    @click="
        $store.globalLoader.show('Memproses permintaan...');
        setTimeout(() => {
            // Simulate processing
            $store.globalLoader.hide();
            alert('Selesai!');
        }, 2000);
    "
    class="bg-blue-500 text-white px-4 py-2 rounded">
    Proses Data
</button>
```

### Button with Livewire Action:

```html
<button 
    wire:click="processData"
    @click="$store.globalLoader.show('Memproses data...')"
    class="bg-green-500 text-white px-4 py-2 rounded">
    Proses Data
</button>
```

**In the Livewire component:**

```php
public function processData()
{
    try {
        // Your processing logic here
        sleep(2); // Simulate processing time
        
        // Dispatch success event
        $this->dispatch('hide-global-loader');
        $this->dispatch('show-success-message', message: 'Data berjaya diproses!');
        
    } catch (Exception $e) {
        $this->dispatch('hide-global-loader');
        $this->dispatch('show-error-message', message: 'Ralat: ' . $e->getMessage());
    }
}
```

## 5. File Upload Integration

### With Livewire File Upload:

```html
<div x-data="{ uploading: false }">
    <input 
        type="file" 
        wire:model="document"
        @change="uploading = true; $store.globalLoader.show('Memuat naik dokumen...')"
        accept=".pdf,.doc,.docx">
    
    <div wire:loading wire:target="document">
        <!-- This will show while Livewire processes the file -->
    </div>
</div>
```

**In the Livewire component:**

```php
public $document;

public function updatedDocument()
{
    $this->validate([
        'document' => 'required|file|max:10240', // 10MB max
    ]);
    
    // Process the uploaded file
    $path = $this->document->store('documents');
    
    // Hide loader and show success
    $this->dispatch('hide-global-loader');
    $this->dispatch('show-success-message', message: 'Dokumen berjaya dimuat naik!');
}
```

## 6. Search/Filter Integration

### Real-time Search with Debouncing:

```html
<div x-data="{ 
    searchTerm: '', 
    searchTimeout: null,
    searching: false 
}">
    <input 
        type="text" 
        x-model="searchTerm"
        @input="
            clearTimeout(searchTimeout);
            searching = true;
            $store.globalLoader.show('Mencari...');
            searchTimeout = setTimeout(() => {
                $wire.search(searchTerm).then(() => {
                    searching = false;
                    $store.globalLoader.hide();
                });
            }, 500);
        "
        placeholder="Cari peniaga..."
        class="w-full border rounded px-3 py-2">
</div>
```

## 7. Navigation Integration

### Page Navigation with Loader:

```html
<a href="/merchant/{{ $merchant->id }}" 
   @click="$store.globalLoader.show('Memuat halaman peniaga...')"
   class="text-blue-600 hover:underline">
    Lihat Peniaga
</a>
```

### Back Button with Loader:

```html
<button 
    onclick="
        GlobalLoader.show('Kembali ke dashboard...');
        window.location.href='/dashboard';
    "
    class="bg-gray-500 text-white px-4 py-2 rounded">
    <x-heroicon-o-arrow-long-left class="mr-2 h-5 w-5" /> Kembali
</button>
```

## 8. Error Handling Best Practices

### Complete Error Handling Example:

```javascript
async function saveData(data) {
    try {
        GlobalLoader.show('Menyimpan data...');
        
        const response = await fetch('/api/save-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(data),
            showLoader: false // We're handling loader manually
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        // Success
        GlobalLoader.hide();
        Swal.fire({
            icon: 'success',
            title: 'Berjaya!',
            text: 'Data telah disimpan.',
            timer: 2000
        });
        
    } catch (error) {
        GlobalLoader.hide();
        Swal.fire({
            icon: 'error',
            title: 'Ralat!',
            text: 'Gagal menyimpan data: ' + error.message
        });
    }
}
```

## 9. Testing the Integration

### Simple Test Function:

```javascript
// Add this to browser console to test
function testGlobalLoader() {
    console.log('Testing Global Loader...');
    
    // Test basic show/hide
    GlobalLoader.show('Testing...');
    setTimeout(() => {
        GlobalLoader.hide();
        console.log('Basic test completed');
    }, 2000);
}

// Run the test
testGlobalLoader();
```

### Integration Test with Events:

```javascript
// Listen for loader events
window.addEventListener('global-loader:shown', (e) => {
    console.log('✅ Loader shown:', e.detail.message);
});

window.addEventListener('global-loader:hidden', () => {
    console.log('✅ Loader hidden');
});

// Test with custom message
GlobalLoader.show('Testing custom message...');
setTimeout(() => GlobalLoader.hide(), 1500);
```

## Notes

1. **Always pair show() with hide()**: Every `GlobalLoader.show()` should have a corresponding `GlobalLoader.hide()`
2. **Use try-catch blocks**: Always hide the loader in finally blocks or catch blocks
3. **Provide meaningful messages**: Use descriptive messages in Bahasa Malaysia
4. **Test error scenarios**: Ensure the loader hides even when operations fail
5. **Consider user experience**: Don't show loaders for very quick operations (< 500ms)

## Troubleshooting

- **Loader not showing**: Check browser console for JavaScript errors
- **Loader not hiding**: Ensure all code paths call `hide()` or dispatch the hide event
- **Alpine.js errors**: Make sure Alpine.js is loaded before the global loader script
- **Styling issues**: Check that Tailwind CSS classes are properly compiled
