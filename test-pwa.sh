#!/bin/bash

# PWA Testing Script for BizAppos MBI Dashboard
# This script helps verify PWA implementation

echo "🚀 PWA Testing Script for BizAppos MBI Dashboard"
echo "================================================"

# Check if required files exist
echo "📁 Checking PWA files..."

files=(
    "public/manifest.json"
    "public/sw.js"
    "public/js/pwa-install.js"
    "public/offline.html"
    "public/icons/icon-192x192.png"
    "public/icons/icon-512x512.png"
)

missing_files=()

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file missing"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "✅ All PWA files are present"
else
    echo "❌ Missing files: ${missing_files[*]}"
    exit 1
fi

echo ""
echo "🔍 Validating manifest.json..."

# Check if manifest.json is valid JSON
if jq empty public/manifest.json 2>/dev/null; then
    echo "✅ manifest.json is valid JSON"
else
    echo "❌ manifest.json is invalid JSON"
    exit 1
fi

echo ""
echo "🌐 Starting local server for testing..."

# Check if Laravel server is running
if curl -s http://localhost:8000 > /dev/null; then
    echo "✅ Laravel server is already running on http://localhost:8000"
else
    echo "🚀 Starting Laravel development server..."
    php artisan serve --host=0.0.0.0 --port=8000 &
    SERVER_PID=$!
    echo "Server started with PID: $SERVER_PID"
    sleep 3
fi

echo ""
echo "📱 Testing Instructions:"
echo "========================"
echo ""
echo "1. Desktop Testing:"
echo "   - Open http://localhost:8000 in Chrome"
echo "   - Login to the dashboard"
echo "   - Open DevTools > Application > Manifest"
echo "   - Check Service Workers tab"
echo "   - Visit http://localhost:8000/pwa-test for detailed testing"
echo ""
echo "2. Mobile Testing:"
echo "   - Find your local IP address: $(hostname -I | awk '{print $1}' 2>/dev/null || ifconfig | grep 'inet ' | grep -v 127.0.0.1 | awk '{print $2}' | head -1)"
echo "   - Open http://$(hostname -I | awk '{print $1}' 2>/dev/null || ifconfig | grep 'inet ' | grep -v 127.0.0.1 | awk '{print $2}' | head -1):8000 on your mobile device"
echo "   - Login to the dashboard"
echo "   - Wait for the install prompt to appear"
echo ""
echo "3. PWA Test Interface:"
echo "   - Visit /pwa-test after logging in"
echo "   - Check all status indicators"
echo "   - Use test buttons to verify functionality"
echo ""
echo "📋 Checklist for Testing:"
echo "========================="
echo "□ Service worker registers successfully"
echo "□ Manifest loads without errors"
echo "□ Install prompt appears on mobile after login"
echo "□ App can be installed to home screen"
echo "□ App works offline (test by disabling network)"
echo "□ Icons display correctly"
echo "□ App opens in standalone mode when installed"
echo ""
echo "🔧 Troubleshooting:"
echo "==================="
echo "- If install prompt doesn't appear:"
echo "  * Check browser console for errors"
echo "  * Verify you're on a mobile device"
echo "  * Ensure you're logged in"
echo "  * Clear browser cache and localStorage"
echo ""
echo "- If service worker fails:"
echo "  * Check browser DevTools > Application > Service Workers"
echo "  * Look for registration errors in console"
echo "  * Verify all cached files are accessible"
echo ""
echo "Press Ctrl+C to stop the server when done testing"

# Keep script running
if [ ! -z "$SERVER_PID" ]; then
    wait $SERVER_PID
fi