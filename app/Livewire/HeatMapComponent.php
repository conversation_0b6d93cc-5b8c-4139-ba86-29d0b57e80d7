<?php

namespace App\Livewire;

use Livewire\Component;

class HeatMapComponent extends Component
{
    public $initializeHeatmap;

    public $locationData = [
        ['lat' => 3.1234, 'lng' => 101.5678, 'count' => 10],
        ['lat' => 3.2345, 'lng' => 101.6789, 'count' => 15],
        ['lat' => 3.3456, 'lng' => 101.7890, 'count' => 8],
        ['lat' => 3.4567, 'lng' => 101.8901, 'count' => 12],
        ['lat' => 3.5678, 'lng' => 101.9012, 'count' => 20],
    ];

    public function initializeHeatmap()
    {
        $this->dispatchBrowserEvent('initializeHeatmap', [
            'locationData' => $this->locationData,
            'config' => [
                'radius' => 25,
                'maxOpacity' => 0.8,
                'scaleRadius' => true,
                'useLocalExtrema' => false,
            ],
        ]);
    }
}
