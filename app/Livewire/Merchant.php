<?php

namespace App\Livewire;

use App\Models\Company;
use App\Models\Order;
use App\Models\PBT;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Illuminate\Support\Facades\Http;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')] // Specify the layout to use
class Merchant extends Component
{
    public $userId;
    public $username;
    public $profile;
    public $pbt;
    public $topProducts;
    public $totalSales;
    public $lastActivity;
    public $paymentMethods;
    public $dailySales;

    public function mount($user_id)
    {
        $this->userId = $user_id;
        $this->merchantDetails($this->userId);
    }

    public function merchantDetails()
    {
        // Fetch profile
        // $this->profile = Company::where('user_id', $this->userId)->first();
        $this->profile = Company::with('user:id,username')->where('user_id',$this->userId)->first();

        $this->pbt = PBT::where('id', $this->profile->pbt_id)->first();

        // Fetch top products
        $topProducts = Order::query()
            ->selectRaw('
                order_details.productName as name,
                order_details.product_SKU as sku,
                sum(order_details.product_qty) as quantity, 
                FORMAT(sum(order_details.product_price_decimal * order_details.product_qty), 2) as total
            ')
            ->join('order_details', 'order_details.order_id', '=', 'orders.id')
            ->whereNull('orders.deleted_at')
            ->whereNull('order_details.deleted_at')
            ->where('orders.user_id', '=', $this->userId)
            ->groupBy('name', 'sku')
            ->orderBy('quantity', 'desc')
            ->limit(5)
            ->get();

        // Transform the data
        $this->topProducts = [
            'series' => $topProducts->pluck('quantity')->toArray(), // Extract quantities
            'labels' => $topProducts->pluck('name')->toArray(), // Extract product names
        ];

        // Fetch last activity
        $this->lastActivity = DB::table(function ($query) {
            $query->select('users.updated_at', DB::raw('"users" as source_table'))
                ->from('users')
                ->where('users.id', '=', $this->userId)
                ->unionAll(
                    DB::table('companies')
                        ->select('companies.updated_at', DB::raw('"companies" as source_table'))
                        ->join('users', 'companies.user_id', '=', 'users.id')
                        ->where('companies.user_id', '=', $this->userId)
                )
                ->unionAll(
                    DB::table('products')
                        ->select('products.updated_at', DB::raw('"products" as source_table'))
                        ->join('companies', 'products.parent_company', '=', 'companies.id')
                        ->join('users', 'companies.user_id', '=', 'users.id')
                        ->where('companies.user_id', '=', $this->userId)
                )
                ->unionAll(
                    DB::table('orders')
                        ->select('orders.updated_at', DB::raw('"orders" as source_table'))
                        ->join('companies', 'orders.company_id', '=', 'companies.id')
                        ->join('users', 'companies.user_id', '=', 'users.id')
                        ->where('companies.user_id', '=', $this->userId)
                )
                ->unionAll(
                    DB::table('log_stocks')
                        ->select('log_stocks.updated_at', DB::raw('"log_stocks" as source_table'))
                        ->join('users', 'log_stocks.user_id', '=', 'users.id')
                        ->where('log_stocks.user_id', '=', $this->userId)
                );
        })
        ->orderBy('updated_at', 'desc')
        ->limit(1)
        ->first();

        // Fetch total sales
        $this->totalSales = Order::where('user_id', $this->userId)
            ->whereNull('deleted_at')
            ->sum('grandtotal_decimal');

        // Fetch payment methods
        $allPaymentMethod = Order::selectRaw(
            'pay_method, SUM(payment_received_decimal) as value, COUNT(*) as quantity'
        )
        ->whereNull('deleted_at')
        ->where('user_id', $this->userId)
        ->groupBy('pay_method')
        ->havingRaw('value > 0 OR quantity > 1')
        ->orderBy('value', 'DESC')
        ->get();

        // Calculate total quantity for percentage calculation
        $totalQuantity = $allPaymentMethod->sum('quantity');

        $this->paymentMethods = [];
        foreach ($allPaymentMethod as $key => $item) {
            $name = ($item->pay_method === 'QR_PAY') ? 'QR_GKASH' : $item->pay_method;
            $number = ($key == 0) ? '' : $key + 1;
            $backgroundColors = [
                "!bg-primary-info" => "#ef4444",
                "!bg-primary-info2" => "#f97316",
                "!bg-primary-info3" => "#fcd34d",
                "!bg-primary-info4" => "#84cc16",
                "!bg-primary-info5" => "#4ade80",
                "!bg-primary-info6" => "#22d3ee",
                "!bg-primary-info7" => "#3b82f6",
                "!bg-primary-info8" => "#818cf8",
                "!bg-primary-info9" => "#e879f9",
                "!bg-primary-info10" => "#fb7185",
            ];

            $background = isset($backgroundColors["!bg-primary-info" . $number]) ? $backgroundColors["!bg-primary-info" . $number] : "!bg-primary-info";

            // Calculate percentage based on quantity
            $percentage = $totalQuantity > 0 ? ($item->quantity / $totalQuantity) * 100 : 0;

            $this->paymentMethods[$item->pay_method] = [
                'name' => $name,
                'quantity' => $item->quantity,
                'amount' => $item->value,
                'background' => $background,
                'percentage' => number_format($percentage, 2) . '%', // Add percentage
            ];
        }

        // dd($this->paymentMethods);

        // Fetch daily sales for the last 7 days
        $dates = collect();
        for ($i = 6; $i >= 0; $i--) {
            $dates->push(Carbon::now()->subDays($i)->toDateString());
        }

        $dailySalesLast7Days = DB::table('orders')
            ->select(DB::raw('DATE(order_date) as date'), DB::raw('SUM(grandtotal_decimal) as total_sales'))
            ->where('company_id', $this->profile->id)
            ->whereNull('deleted_at')
            ->whereBetween('order_date', [
                Carbon::now()->subDays(6)->startOfDay(),
                Carbon::now()->endOfDay()
            ])
            ->groupBy(DB::raw('DATE(order_date)'))
            ->orderBy('date', 'asc')
            ->get()
            ->keyBy('date');

        $this->dailySales = $dates->map(function ($date) use ($dailySalesLast7Days) {
            return [
                'date' => $date,
                'sale' => $dailySalesLast7Days->get($date)->total_sales ?? '0.00'
            ];
        });
        // dd($this->dailySales);
    }

    public function render()
    {
        // Prepare data for the chart
        $labels = $this->dailySales->pluck('date')->toArray(); // Extract dates
        $data = $this->dailySales->pluck('sale')->toArray();   // Extract sales values
        
        return view('livewire.merchant', [
            'username' => $this->profile->username,
            'profile' => $this->profile,
            'pbt' => $this->pbt,
            'topProducts' => $this->topProducts,
            'totalSales' => $this->totalSales,
            'lastActivity' => $this->lastActivity,
            'paymentMethods' => $this->paymentMethods,
            'dailySales' => $this->dailySales,
            'labels' => $labels,
            'data' => $data,
        ]);
    }
}