<?php

namespace App\Models\Payment;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\UUID;

class Gkash extends Model
{
    use HasFactory,UUID;

    protected $fillable = [
        'order_id',
        'reference',
        'pid',
        'status',
        'domain',
        'cartID',
        'payment_method',
        'pay_amount',
        'CID',
        'POID',
        'description',
        'PaymentType'
    ];
}
