<?php

namespace App\Models;

use App\Models\User;
use App\Traits\UUID;
use App\Models\Order;
use App\Models\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CommerceasiaPay extends Model
{
    use HasFactory, UUID;

    protected $guarded = [];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

}
