<?php

namespace App\Models;

use App\Traits\UUID;
use App\Models\Company;
use App\Enums\EmployeeEnum;
use App\Models\CompanyBranch;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Employee extends Model
{
    use HasFactory, UUID;

    protected $fillable = [
        'user_id',
        'boss_id',
        'company_id',
        'emp_jobTitle',
        'emp_status',
        'total_sales',
        'join_date',
    ];

    protected $casts = [
  
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function comBranch()
    {
        return $this->hasOne(CompanyBranch::class,'person_in_charge');
    }
}
