<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Traits\UUID;
use App\Models\Order;
use App\Enums\UserEnum;
use App\Models\Receipt;
use App\Models\Employee;
use App\Models\CommerceasiaPay;
use App\Models\CommerceasiaToken;
use Illuminate\Support\Facades\Hash;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use HasFactory, Notifiable, UUID;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'username',
        'email',
        'password',
        'pid',
        'isBizappUser',
        'domain',
        'access_module',
        'migration_status'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * Always encrypt the password when it is updated.
     *
     * @param $value
    * @return string
    */

    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = bcrypt($value);
    }

    public function getRouteKeyName()
    {
        return 'id';
    }


    public function userDetails()
    {
       return $this->hasOne(UserDetail::class);
    }

    public function companies()
    {
        return $this->hasOne(Company::class);
    }

    public function employee()
    {
        return $this->hasOne(Employee::class);
    }

    public function receipts()
    {
        return $this->hasOne(Receipt::class,'user_id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class,'user_id');
    }
    
    public function ca_pay()
    {
        return $this->hasMany(CommerceasiaPay::class);
    }

    public function ca_token()
    {
        return $this->hasOne(CommerceasiaToken::class)->whereNull('deleted_at');
    }
}
