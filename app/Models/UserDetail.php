<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Enums\UserEnum;
use App\Traits\UUID;

class UserDetail extends Model
{
    use HasFactory, UUID;

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'mobile',
        'avatar',
        'address',
        'city',
        'state',
        'postcode',
        'country',
        'currency',
        'bizapp_secretkey',
        'status',
        'device_info',
        'app_version',
        'bizapp_exp_date',
        'device_brand',
        'device_id',
        'db_version',
        'device_os'
    ];

    protected $casts = [
      
    ];

    public function getRouteKeyName()
    {
        return 'user_id';
    }

    public function user()
    {
        return $this->belongsTo(User::class,'user_id');
    }
}
