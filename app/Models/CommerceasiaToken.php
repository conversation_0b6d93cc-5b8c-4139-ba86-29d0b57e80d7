<?php

namespace App\Models;

use App\Models\User;
use App\Traits\UUID;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CommerceasiaToken extends Model
{
    use HasFactory, UUID, SoftDeletes;
    protected $table = 'commerceasia_tokens'; 
    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
