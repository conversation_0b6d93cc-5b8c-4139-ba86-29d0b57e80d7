<?php

namespace App\Models;

use Exception;
use Carbon\Carbon;
use App\Models\User;
use App\Traits\UUID;
use App\Models\Company;
use App\Models\OrderDetail;
use App\Models\Payment\Gkash;
use App\Models\CommerceasiaPay;
use App\Models\OrderLocationLog;
use App\Models\Payment\GkashUAV;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $fillable = [
        'user_id',
        'bizapp_order_id',
        'bizapp_temp_id',
        'compaign_id',
        'company_id',
        'customer_id',
        'customer_name',
        'order_number',
        'pay_method',
        'cap_id',
        'cartID',
        'pay_collection_id',
        'roundingAdjustment',
        'subtotal',
        'payment_received',
        'payment_balance',
        'grandtotal',
        'discounts',
        'roundingAdjustment_decimal',
        'subtotal_decimal',
        'payment_received_decimal',
        'payment_balance_decimal',
        'grandtotal_decimal',
        'discounts_decimal',
        'order_label',
        'order_image',
        'order_notes',
        'order_latitude',
        'order_longitude',
        'order_date',
        'status',
        'payment_provider',
        'coupon_code',
        'coupon_value',
        'coupon_for',
        'sst_value',
        'sst_amount',
        'order_items'
    ];

    // public static function boot()
    // {
    //     parent::boot();

    //     static::creating(function ($model) {
    //         $model->order_number = self::generateOrderNumber();
    //     });
    // }

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function orderDetails()
    {
        return $this->hasMany(OrderDetail::class);
    }

    public function gkash()
    {
        return $this->hasOne(Gkash::class, 'order_id', 'bizapp_temp_id');
    }

    public static function generateOrderNumber()
    {
        $date = now()->format('Ymd');
        $count = self::select('created_at')->whereDate('created_at', now()->toDateString())
                            ->orderBy('created_at', 'desc')
                            ->count();

        
        $newNumber = str_pad($count + 1, 4, '0', STR_PAD_LEFT);
        return $date . $newNumber;
    }

    public function users()
    {
        return $this->belongsTo(User::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class,'company_id');
    }

    public function location_log()
    {
        return $this->hasOne(OrderLocationLog::class);
    }

    public function scopeTotalSalesReport($query, $companyId)
    {
        try {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();

            // If today's date is between the start and end dates (current month), limit the end date to today.
            if (Carbon::today()->between($startDate, $endDate)) {
                $endDate = Carbon::today();
            }

            return $query->where('company_id', $companyId)
                ->whereBetween('order_date', [$startDate, $endDate])
                ->selectRaw('DATE_FORMAT(order_date, "%d/%m/%Y") as date, SUM(grandtotal_decimal) as total_sales')
                ->groupBy('date')
                ->orderBy('date', 'DESC');
        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()]);
        }
    }

    public static function getSalesReportByPaymentMethod($companyId, $date)
    {
        $payMethods = [
            'CASH',
            'DEBITCARD',
            'CREDITCARD',
            'BANKTRANSFER',
            'TOUCHNGO',
            'QRPAY',
            'Credit/Debit(NFC)',
            'ATOME',
            'GRABPAY',
            'BNPL',
            'EWALLET',
            'ScanQRGkash',
            'OTHERS'
        ];
        $counts = array_fill_keys($payMethods, 0);
        $totals = array_fill_keys($payMethods, 0.00);

        $orders = self::select('pay_method', 'grandtotal_decimal')
            ->where('user_id', $companyId)
            ->whereBetween('order_date', [$date . ' 00:00:00', $date . " 23:59:59"])
            ->whereNull('deleted_at')
            ->get();
        
        foreach ($orders as $order) {
            $payMethod = $order->pay_method;
            $grandtotal = $order->grandtotal_decimal;

            if (isset($counts[$payMethod])) {
                $counts[$payMethod]++;
                $totals[$payMethod] += $grandtotal;
            }
        }
        // Format the sums to 2 decimal places
        foreach ($totals as $payMethod => $total) {
            $totals[$payMethod] = number_format($total, 2, '.', '');
        }
        
        $total_count = number_format(array_sum($totals), 2, '.', ',');
        $trx = array_sum($counts);

        // Mapping array to rename output keys
        $renameMapping = [
            'BANKTRANSFER' => 'BANK TRANSFER',
            'DEBITCARD' => 'DEBIT CARD',
            'CREDITCARD' => 'CREDIT CARD',
            'TOUCHNGO' => 'TOUCH N GO',
            'Credit/Debit(NFC)' => 'NFC',
            'ScanQRGkash' => 'QR GKASH',
            'EWALLET' => 'E-WALLET'
        ];

        // Apply renaming to the final output
        $formattedCounts = [];
        $formattedTotals = [];

        foreach ($counts as $key => $value) {
            $newKey = $renameMapping[$key] ?? $key; // Use mapped name if available
            $formattedCounts[$newKey] = $value;
            $formattedTotals[$newKey] = number_format($totals[$key],2, '.',',');
        }

        return [
            'total' => $total_count,
            'trx' => $trx,
            'occurance' => $formattedCounts,
            'value' => $formattedTotals
        ];
    }

}


