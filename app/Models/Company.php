<?php

namespace App\Models;

use App\Models\PBT;
use App\Traits\UUID;
use App\Models\Order;
use App\Enums\CompanyEnums;
use App\Models\CompanyBranch;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Company extends Model
{
    use HasFactory, UUID;

    protected $fillable = [
        'user_id',
        'com_name',
        'com_address',
        'com_city',
        'com_state',
        'com_postcode',
        'com_country',
        'com_registration_no',
        'com_mobile',
        'com_email',
        'com_sst_value',
        'com_sst_number',
        'status',
        'category_id',
        'pbt_id'
    ];

    protected $appends = ['state_name'];

    protected $casts = [
   
    ];

    public function user()
    {
        return $this->belongsTo(User::class,'user_id');
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function branches()
    {
        return $this->hasMany(CompanyBranch::class,'branch_of');
    }

    public function pbts()
    {
        return $this->belongsTo(PBT::class,'pbt_id');
    }

    public function getStateNameAttribute()
    {
        // Define the state code-to-name mapping
        $states = [
            '1' => 'Johor',
            '2' => 'Kedah',
            '3' => 'Kelantan',
            '4' => 'Melaka',
            '5' => 'Negeri Sembilan',
            '6' => 'Pahang',
            '7' => 'Penang',
            '8' => 'Perak',
            '9' => 'Perlis',
            '10' => 'Selangor',
            '11' => 'Terengganu',
            '12' => 'Sabah',
            '13' => 'Sarawak',
            '14' => 'Kuala Lumpur',
            '15' => 'Labuan',
            '16' => 'Putrajaya'
        ];

        // Return the corresponding state name or 'Unknown' if not found
        return $states[$this->com_state] ?? 'Unknown';
    }

    public function matchPbtByAddress()
    {
        // List of Selangor PBTs with their matching keywords
        $selangorPbts = [
            [
                'keywords' => ['shah alam','sungai buloh','setia alam'],
                'name' => 'Majlis Bandaraya Shah Alam',
                'code' => 'MBSA'
            ],
            [
                'keywords' => ['klang'],
                'name' => 'Majlis Bandaraya Diraja Klang',
                'code' => 'MBDK'
            ],
            [
                'keywords' => ['petaling jaya', 'pj'],
                'name' => 'Majlis Bandaraya Petaling Jaya',
                'code' => 'MBPJ'
            ],
            [
                'keywords' => ['subang jaya','subang','puchong','kinrara'],
                'name' => 'Majlis Bandaraya Subang Jaya',
                'code' => 'MBSJ'
            ],
            [
                'keywords' => ['sepang','putra perdana'],
                'name' => 'Majlis Perbandaran Sepang',
                'code' => 'MPSp'
            ],
            [
                'keywords' => ['kajang','bangi','beranang','semenyih','mahkota cheras'],
                'name' => 'Majlis Perbandaran Kajang',
                'code' => 'MPKj'
            ],
            [
                'keywords' => ['selayang','batu caves','rawang','kepong'],
                'name' => 'Majlis Perbandaran Selayang',
                'code' => 'MPS'
            ],
            [
                'keywords' => ['ampang'],
                'name' => 'Majlis Perbandaran Ampang Jaya',
                'code' => 'MPAJ'
            ],
            [
                'keywords' => ['kuala selangor','puncak alam'],
                'name' => 'Majlis Perbandaran Kuala Selangor',
                'code' => 'MPKS'
            ],
            [
                'keywords' => ['sabak bernam','sungai panjang','sungai besar','sekinchan'],
                'name' => 'Majlis Daerah Sabak Bernam',
                'code' => 'MDSB'
            ],
            [
                'keywords' => ['hulu selangor','serendah','batang kali','kuala kubu','kuala kubu bharu','ulu yam','batang kali'],
                'name' => 'Majlis Perbandaran Hulu Selangor',
                'code' => 'MPHS'
            ],
            [
                'keywords' => ['kuala langat','banting','panglima garang','jenjarom'],
                'name' => 'Majlis Perbandaran Kuala Langat',
                'code' => 'MPKL'
            ]
        ];

        // Check if PBT is already set
        if ($this->pbt_id) {
            return false;
        }

        // Convert address to lowercase for case-insensitive matching
        $addressLower = strtolower($this->com_address);

        // Try to match PBT
        foreach ($selangorPbts as $pbt) {
            foreach ($pbt['keywords'] as $keyword) {
                if (strpos($addressLower, $keyword) !== false) {
                    // Find the PBT in the database
                    $matchedPbt = PBT::where('name', $pbt['name'])
                        ->where('code', $pbt['code'])
                        ->first();

                    if ($matchedPbt) {
                        $this->pbt_id = $matchedPbt->id;
                        $this->save();
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public static function updateMissingPbtIds()
    {
        $companiesWithoutPbt = self::whereNull('pbt_id')->get();
        
        foreach ($companiesWithoutPbt as $company) {
            $company->matchPbtByAddress();
        }
    }

    public function getPbtCodeAttribute()
    {
        return $this->pbts->code ?? null;
    }

    // Override the toArray method to place 'state_name' directly under com_state
    public function toArray()
    {
        // Start by converting the parent model to an array
        $array = parent::toArray();

        // Explicitly define the order you want
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'category_id' => $this->category_id,
            'com_name' => $this->com_name,
            'com_address' => $this->com_address,
            'com_city' => $this->com_city,
            'com_state' => $this->com_state,
            'state_name' => $this->state_name, // Place state_name right after com_state
            'com_postcode' => $this->com_postcode,
            'com_country' => $this->com_country,
            'com_registration_no' => $this->com_registration_no ?? '-',
            'com_mobile' => $this->com_mobile,
            'com_email' => $this->com_email,
            'com_sst_value' => $this->com_sst_value,
            'com_sst_number' => $this->com_sst_number ?? '-',
            'pbt_id' => $this->pbt_id,
            'pbt_code' => $this->pbtCode,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
