<?php

namespace App\Models;

use App\Traits\UUID;
use App\Models\Order;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderLocationLog extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $fillable = [
        'order_id',
        'bizapp_order_id',
        'town',
        'city',
        'district',
        'city_district',
        'postcode'
    ];

    public function orders()
    {
        return $this->belongsTo(Order::class);
    }
}
