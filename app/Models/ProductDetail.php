<?php

namespace App\Models;

use App\Traits\UUID;
use App\Models\Product;
use App\Models\ProductsCategory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductDetail extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $fillable = [
        'product_id',
        'category_id',
        'product_desc_html',
        'product_attachment',
        'product_attachment_1',
        'product_attachment_2',
        'product_attachment_thumbnail'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class,'product_id');
    }

    public function productsCategory()
    {
        return $this->belongsTo(ProductsCategory::class,'category_id');
    }
}
