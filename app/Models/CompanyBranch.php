<?php

namespace App\Models;

use App\Traits\UUID;
use App\Models\Employee;
use App\Enums\CompanyEnums;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CompanyBranch extends Model
{
    use HasFactory, UUID;

    protected $fillable = [
        'branch_of',
        'person_in_charge',
        'br_name',
        'br_address',
        'br_city',
        'br_state',
        'br_postcode',
        'br_country',
        'br_registration_no',
        'status',
    ];

    protected $casts = [
       
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class,'person_in_charge');
    }

}
