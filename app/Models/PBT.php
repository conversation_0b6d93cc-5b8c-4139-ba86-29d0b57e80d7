<?php

namespace App\Models;

use App\Traits\UUID;
use App\Models\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PBT extends Model
{
    use HasFactory, UUID;
    protected $guarded = [];
    // Explicitly define the table name to avoid Laravel's pluralization
    protected $table = 'pbts';
    
    public function companies()
    {
        return $this->belongsToMany(Company::class,'pbt_id');
    }
}
