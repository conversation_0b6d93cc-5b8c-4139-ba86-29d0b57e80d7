<?php

namespace App\Models;

use App\Traits\UUID;
use App\Enums\ProductEnums;
use App\Models\ProductDetail;
use App\Models\CollectionProduct;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $fillable = [
        'parent_company',
        'branch_id',
        'product_id_bizapp',
        'product_name',
        'product_SKU',
        'product_brand',
        'product_description',
        'product_note',
        'product_stock',
        'product_stock_status',
        'product_price',
        'cost_price',
        'product_weight',
        'product_fav',
        'product_status',
        'isComposite',
        'material_ids',
        'category_id',
    ];

    protected $casts = [
        'material_ids' => 'array',
    ];

    public function companyOf()
    {
        return $this->belongsTo(Company::class,'parent_company');
    }

    public function productDetail()
    {
        return $this->hasOne(ProductDetail::class);
    }

    public function category(){
        return $this->belongsTo(ProductsCategory::class);
    }

    public function collectionProducts()
    {
        return $this->hasMany(CollectionProduct::class);
    }
}
