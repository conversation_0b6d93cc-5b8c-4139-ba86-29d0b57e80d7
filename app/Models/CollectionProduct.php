<?php

namespace App\Models;

use App\Traits\UUID;
use App\Models\Product;
use App\Models\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CollectionProduct extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $fillable = [
        'product_id',
        'collection_id'
    ];

    public function products()
    {
        return $this->belongsTo(Product::class,'product_id');
    }
}
