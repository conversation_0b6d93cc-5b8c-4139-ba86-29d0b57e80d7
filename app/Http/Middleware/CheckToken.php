<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Semak jika token wujud dalam header 'Authorization' atau dalam query string 'token'
        $token = $request->header('Authorization') ?? $request->query('token');

        // if no token, return response error
        if (!$token) {
            return response()->json(['error' => 'Token is required'], 401);
        }

        return $next($request);
    }
}
