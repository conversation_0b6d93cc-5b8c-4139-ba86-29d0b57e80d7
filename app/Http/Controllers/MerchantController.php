<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class MerchantController extends Controller
{
    
    // merchant list
    public function index() {

        return view('merchant.index');

    }

    // show merchant details
    public function show($userId) {

        $top_products = [];
        $payment_channels = [];
        $daily_sales = [];
        $total_channel = 0;

        $curl = curl_init();
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer '.session('authToken')
        ];

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('API_URL').'/merchant/index?user_id='.$userId,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => $headers,
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        
        $data = json_decode($response);
     
        if($data->top_product !== []){
            foreach($data->top_product as $product) {
                $top_products['series'][] = $product->quantity;
                $top_products['labels'][] = $product->name;
            }
        }

        $total_channel = array_sum(array_column($data->payment_method, 'quantity')); // sum

        foreach($data->payment_method as $channel) {

            $percentage = ($channel->quantity / $total_channel) * 100;

            $payment_channels[$channel->name]['quantity'] = $channel->quantity;
            $payment_channels[$channel->name]['percentage'] = number_format($percentage, 2).'%';
            $payment_channels[$channel->name]['color'] = $channel->background;
            $payment_channels[$channel->name]['amount'] = $channel->amount;
        }

        foreach($data->daily_sales as $dailysales) {
            $daily_sales['date'][] = $dailysales->date;
            $daily_sales['sale'][] = $dailysales->sale;
        }

        return view('merchant.show', compact('data', 'top_products', 'payment_channels','daily_sales'));

    }
}
