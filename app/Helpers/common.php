<?php


// return session value by key
if( !function_exists('userInfo')) {

    function userInfo($key) {

        $session = session('user');
        return $session->$key;
    }
}

// format money
if( !function_exists('moneyFormat')) {

    function moneyFormat($value = 0.00, $decimal_point = 2, $currency = 'RM') {

        $value = floatval($value);
        $value = number_format($value, $decimal_point);

        if ($value < 0) {
            return '('.str_replace('-', '', $value).')';
        }

        return $currency.' '.$value;

    }

}