// PWA Status Indicator
class PWAStatus {
    constructor() {
        this.init();
    }

    init() {
        // Check if running as PWA
        if (this.isPWA()) {
            this.addPWAIndicator();
        }

        // Listen for installation
        window.addEventListener('appinstalled', () => {
            this.addPWAIndicator();
        });
    }

    isPWA() {
        return window.matchMedia('(display-mode: standalone)').matches || 
               window.navigator.standalone === true;
    }

    addPWAIndicator() {
        // Add a small indicator to show the app is running as PWA
        const indicator = document.createElement('div');
        indicator.innerHTML = `
            <div class="fixed top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full shadow-lg z-50 flex items-center space-x-1">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z"/>
                </svg>
                <span>PWA</span>
            </div>
        `;
        
        // Only show for a few seconds
        document.body.appendChild(indicator);
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 3000);
    }
}

// Initialize PWA status
document.addEventListener('DOMContentLoaded', () => {
    new PWAStatus();
});