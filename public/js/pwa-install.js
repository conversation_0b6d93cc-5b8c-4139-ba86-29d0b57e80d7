class PWAInstaller {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isPromptShown = localStorage.getItem('pwa-prompt-shown') === 'true';
        this.isPromptDismissed = localStorage.getItem('pwa-prompt-dismissed') === 'true';
        this.init();
    }

    init() {
        // Check if app is already installed
        this.checkIfInstalled();
        
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallPrompt();
        });

        // Listen for app installed event
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            this.isInstalled = true;
            localStorage.setItem('pwa-installed', 'true');
            this.hideInstallPrompt();
        });

        // Check if user is on mobile device
        if (this.isMobileDevice() && !this.isInstalled && !this.isPromptShown && !this.isPromptDismissed) {
            // Show prompt after successful login (delay to ensure user is authenticated)
            setTimeout(() => {
                if (this.isUserAuthenticated()) {
                    this.showInstallPrompt();
                }
            }, 2000);
        }
    }

    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    isUserAuthenticated() {
        // Check if user is authenticated by looking for auth indicators
        return document.querySelector('main.main') !== null || 
               window.location.pathname.includes('/dashboard') ||
               window.location.pathname.includes('/merchant') ||
               window.location.pathname.includes('/statistics') ||
               document.body.classList.contains('authenticated');
    }

    checkIfInstalled() {
        // Check if app is installed
        if (localStorage.getItem('pwa-installed') === 'true') {
            this.isInstalled = true;
            return;
        }

        // Check if running in standalone mode
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            this.isInstalled = true;
            localStorage.setItem('pwa-installed', 'true');
        }
    }

    showInstallPrompt() {
        if (this.isInstalled || this.isPromptShown || this.isPromptDismissed) {
            return;
        }

        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/.test(navigator.userAgent);

        let instructions = '';
        if (isIOS) {
            instructions = `
                <div class="text-sm text-gray-600 mt-2">
                    <p class="font-medium mb-2">Untuk iOS:</p>
                    <ol class="list-decimal list-inside space-y-1 text-xs">
                        <li>Tekan butang "Share" <svg class="inline w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"/></svg></li>
                        <li>Pilih "Add to Home Screen"</li>
                        <li>Tekan "Add" untuk memasang</li>
                    </ol>
                </div>
            `;
        } else if (isAndroid) {
            instructions = `
                <div class="text-sm text-gray-600 mt-2">
                    <p class="font-medium mb-2">Untuk Android:</p>
                    <ol class="list-decimal list-inside space-y-1 text-xs">
                        <li>Tekan butang menu (⋮) di browser</li>
                        <li>Pilih "Add to Home screen" atau "Install app"</li>
                        <li>Tekan "Add" atau "Install"</li>
                    </ol>
                </div>
            `;
        }

        const promptHTML = `
            <div id="pwa-install-prompt" class="fixed bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50 max-w-sm mx-auto">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <img src="/images/logo/pos_logo_bak.png" alt="App Icon" class="w-12 h-12 rounded-lg">
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm font-medium text-gray-900">Install Bizappos MBI</h3>
                        <p class="text-xs text-gray-500 mt-1">Akses dashboard dengan mudah dari home screen anda</p>
                        ${instructions}
                    </div>
                </div>
                <div class="flex space-x-2 mt-4">
                    ${this.deferredPrompt ? '<button id="pwa-install-btn" class="flex-1 bg-blue-600 text-white text-sm font-medium py-2 px-3 rounded-md hover:bg-blue-700 transition-colors">Install</button>' : ''}
                    <button id="pwa-dismiss-btn" class="flex-1 bg-gray-100 text-gray-700 text-sm font-medium py-2 px-3 rounded-md hover:bg-gray-200 transition-colors">Tutup</button>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', promptHTML);

        // Add event listeners
        const installBtn = document.getElementById('pwa-install-btn');
        const dismissBtn = document.getElementById('pwa-dismiss-btn');

        if (installBtn) {
            installBtn.addEventListener('click', () => this.installApp());
        }

        if (dismissBtn) {
            dismissBtn.addEventListener('click', () => this.dismissPrompt());
        }

        // Mark prompt as shown
        localStorage.setItem('pwa-prompt-shown', 'true');
        this.isPromptShown = true;

        // Auto-hide after 30 seconds
        setTimeout(() => {
            this.hideInstallPrompt();
        }, 30000);
    }

    async installApp() {
        if (!this.deferredPrompt) {
            return;
        }

        // Show the install prompt
        this.deferredPrompt.prompt();

        // Wait for the user to respond to the prompt
        const { outcome } = await this.deferredPrompt.userChoice;
        
        if (outcome === 'accepted') {
            console.log('User accepted the install prompt');
            localStorage.setItem('pwa-installed', 'true');
        } else {
            console.log('User dismissed the install prompt');
            localStorage.setItem('pwa-prompt-dismissed', 'true');
        }

        // Clear the deferredPrompt
        this.deferredPrompt = null;
        this.hideInstallPrompt();
    }

    dismissPrompt() {
        localStorage.setItem('pwa-prompt-dismissed', 'true');
        this.isPromptDismissed = true;
        this.hideInstallPrompt();
    }

    hideInstallPrompt() {
        const prompt = document.getElementById('pwa-install-prompt');
        if (prompt) {
            prompt.remove();
        }
    }
}

// Initialize PWA installer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PWAInstaller();
});

// Also initialize if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new PWAInstaller();
    });
} else {
    new PWAInstaller();
}